# NST Browser Integration with FB Scheduler

## Overview

تم تطوير نظام متكامل لربط حسابات NST Browser مع برنامج FB Scheduler، مما يتيح للمستخدمين استخدام متصفحات NST Browser المُعدة مسبقاً للنشر التلقائي على Facebook.

## الميزات الجديدة

### 1. دعم NST Browser
- ربط حسابات Facebook مع profiles NST Browser
- إدارة متعددة لأنواع المتصفحات (Chrome العادي و NST Browser)
- اختبار الاتصال مع NST Browser profiles
- نشر تلقائي باستخدام NST Browser

### 2. واجهة إدارة محسنة
- tab جديد "NST Browser" في صفحة الحسابات
- عرض نوع المتصفح وProfile ID في جدول الحسابات
- أزرار اختبار الاتصال والنشر
- ربط سهل بين الحسابات و NST profiles

### 3. نظام نشر ذكي
- اختيار تلقائي للمتصفح المناسب حسب إعدادات الحساب
- دعم النشر بالصور والنصوص
- معالجة أخطاء متقدمة
- سجلات مفصلة للعمليات

## متطلبات النظام

### البرامج المطلوبة
- NST Browser مثبت ويعمل
- Python 3.8+
- المكتبات المطلوبة:
  ```
  requests
  selenium
  PyQt5
  ```

### إعدادات NST Browser
- API Key: `fc63ee6b-0785-4b2a-a179-d6ae22c88479`
- Host: `127.0.0.1`
- Port: `8848`

## طريقة الاستخدام

### 1. إعداد NST Browser Profiles

1. افتح NST Browser
2. أنشئ profiles جديدة أو استخدم الموجودة
3. سجل دخول إلى Facebook في كل profile
4. احفظ Profile IDs للاستخدام لاحقاً

### 2. ربط الحسابات في FB Scheduler

1. افتح FB Scheduler
2. اذهب إلى صفحة "Accounts"
3. اختر tab "NST Browser"
4. أدخل NST Profile ID
5. اختر الحساب المراد ربطه
6. اضغط "Link NST Profile"

### 3. اختبار الاتصال

1. أدخل Profile ID في حقل النص
2. اضغط "Test NST Connection"
3. تأكد من نجاح الاتصال
4. اختبر النشر باستخدام "Test Post with NST"

### 4. استخدام النشر التلقائي

- الحسابات المربوطة بـ NST Browser ستستخدم تلقائياً NST Browser للنشر
- الحسابات العادية ستستخدم Chrome مع الكوكيز المحفوظة
- يمكن مراقبة العملية من خلال السجلات

## الملفات المضافة

### 1. `nst_browser_integration.py`
سكريبت مستقل لإدارة NST Browser profiles:

```bash
# اختبار profile معين
python nst_browser_integration.py test PROFILE_ID

# معالجة profiles من ملف
python nst_browser_integration.py process profiles.txt

# عرض قائمة profiles
python nst_browser_integration.py list-profiles profiles.txt
```

### 2. تحديثات قاعدة البيانات
- إضافة عمود `nst_profile_id` لجدول accounts
- إضافة عمود `browser_type` لتحديد نوع المتصفح

## الدوال الجديدة

### في fb_scheduler.py

#### دوال NST Browser الأساسية
- `launch_and_connect_to_browser()`: تشغيل NST Browser profile
- `exec_selenium_nst()`: الاتصال بـ NST Browser عبر Selenium
- `create_driver_for_account()`: إنشاء driver مناسب حسب نوع الحساب

#### دوال الواجهة
- `link_nst_profile()`: ربط NST profile بحساب
- `test_nst_connection()`: اختبار الاتصال
- `test_nst_post()`: اختبار النشر
- `post_to_facebook_with_account()`: النشر الذكي

#### دوال إدارة البيانات
- `update_account_combo()`: تحديث قائمة الحسابات
- تحديث `load_accounts_data()` لدعم الحقول الجديدة
- تحديث `ManageAccountsTable` لعرض معلومات NST

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **فشل الاتصال بـ NST Browser**
   - تأكد من تشغيل NST Browser
   - تحقق من صحة API Key
   - تأكد من صحة Profile ID

2. **فشل النشر**
   - تأكد من تسجيل الدخول في NST Browser profile
   - تحقق من صحة محتوى المنشور
   - راجع السجلات للتفاصيل

3. **مشاكل قاعدة البيانات**
   - سيتم إضافة الأعمدة الجديدة تلقائياً
   - في حالة المشاكل، احذف قاعدة البيانات وأعد تشغيل البرنامج

## الأمان والخصوصية

- API Key محفوظ في الكود (يمكن تغييره حسب الحاجة)
- لا يتم حفظ كلمات مرور Facebook
- NST Browser profiles تحتفظ بجلسات تسجيل الدخول
- السجلات تحتوي على معلومات مفيدة لاستكشاف الأخطاء

## التطوير المستقبلي

### ميزات مقترحة
- دعم النشر في المجموعات والصفحات المحددة
- جدولة متقدمة حسب نوع المتصفح
- إحصائيات مفصلة لكل نوع متصفح
- نسخ احتياطي لإعدادات NST profiles

### تحسينات تقنية
- تحسين معالجة الأخطاء
- دعم المزيد من أنواع المحتوى
- واجهة أكثر تفاعلية
- دعم APIs إضافية لـ NST Browser

## الدعم

للحصول على المساعدة:
1. راجع السجلات في ملف `error.log`
2. تأكد من تحديث جميع المكتبات المطلوبة
3. تحقق من إعدادات NST Browser
4. اختبر الاتصال قبل استخدام النشر التلقائي
