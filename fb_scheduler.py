import os
import sys
import csv
import sqlite3
import datetime
import shutil
import logging
import json
import random
import glob
import subprocess
import time  # Added for delays
from functools import partial
from dataclasses import dataclass, field # For data structures
from urllib.parse import urlencode, quote
import requests

# --- Configuration & Constants ---
DB_FILE = 'facebook_scheduler.db'
LOG_FILE = 'error.log'
PID_OFFSET = 8309471 # Define as a constant
DEFAULT_THUMBNAIL_SIZE = (100, 100)
SMALL_THUMBNAIL_SIZE = (30, 30)
POST_CATEGORIES = ["Recipes", "Engage", "Parole"] # Use constant

# --- Logging Configuration ---
# (Keep your existing logging setup - it's good)
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - [%(levelname)s] - %(threadName)s - %(message)s", # Added threadName
    filename=LOG_FILE,
    filemode="a"
)
console = logging.StreamHandler(sys.stdout)
console.setLevel(logging.INFO) # Maybe set console to INFO for less noise
formatter = logging.Formatter("%(asctime)s - [%(levelname)s] - %(message)s")
console.setFormatter(formatter)
logging.getLogger().addHandler(console)
logging.info("Application starting...")

# --- Third-Party Imports ---
try:
    import pandas as pd
    from openpyxl import load_workbook
    from PIL import Image
    import chromedriver_autoinstaller
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    import undetected_chromedriver as uc # Using this based on your login code

    from PyQt5 import QtCore, QtGui, QtWidgets
    from PyQt5.QtCore import (Qt, QTimer, QThread, pyqtSignal, QMutex, QMutexLocker,
                            QUrl, QStandardPaths, QSize, QPoint)
    from PyQt5.QtGui import QPixmap, QColor, QIcon, QCursor, QDesktopServices, QPainter # Added QPainter
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QWidget, QSplitter, QStackedWidget, QPushButton,
        QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit, QComboBox, QFileDialog,
        QMessageBox, QCalendarWidget, QDialog, QFormLayout, QInputDialog, QSizePolicy,
        QTableWidget, QTableWidgetItem, QTabWidget, QHeaderView, QTimeEdit, QSpinBox,
        QFrame, QScrollArea, QCheckBox, QStyleOptionButton, QAbstractItemView, QListWidget,
        QGridLayout, QRadioButton, QButtonGroup, QMenu, QAction, QProgressDialog, # Added QProgressDialog
        QStatusBar, QProgressBar # Added StatusBar and ProgressBar
    )
    from PyQt5.QtChart import QChart, QChartView, QPieSeries
except ImportError as e:
    logging.critical(f"Failed to import necessary libraries: {e}. Please install requirements.")
    sys.exit(f"Import Error: {e}. Please ensure all dependencies (pandas, openpyxl, Pillow, selenium, undetected-chromedriver, PyQt5, PyQtChart) are installed.")

# --- Global Database Setup ---
db_mutex = QMutex()

def initialize_database():
    """Initializes the database and returns connection/cursor."""
    try:
        # Ensure thread safety for the connection itself if multiple threads might access it heavily,
        # though typically operations are protected by the mutex. check_same_thread=False is needed for QThread access.
        conn = sqlite3.connect(DB_FILE, check_same_thread=False)
        conn.row_factory = sqlite3.Row # Use Row factory for dict-like access
        c = conn.cursor()
        # Use PRAGMA for potential performance improvement and WAL mode
        c.execute("PRAGMA journal_mode=WAL;")
        c.execute("PRAGMA foreign_keys = ON;") # Enforce foreign key constraints if using them

        # Create tables if they don't exist
        c.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                fb_id TEXT UNIQUE NOT NULL, -- Made fb_id unique and not null
                full_name TEXT,
                cookie TEXT,
                status TEXT DEFAULT 'live', -- Added status with default
                nst_profile_id TEXT, -- NST Browser profile ID
                browser_type TEXT DEFAULT 'chrome' -- 'chrome' or 'nst'
            );
        ''')
        # Add index on fb_id for faster lookups
        c.execute("CREATE INDEX IF NOT EXISTS idx_accounts_fb_id ON accounts (fb_id);")

        # Add new columns for NST Browser support if they don't exist
        try:
            c.execute("ALTER TABLE accounts ADD COLUMN nst_profile_id TEXT")
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            c.execute("ALTER TABLE accounts ADD COLUMN browser_type TEXT DEFAULT 'chrome'")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Create pages table for extracted Facebook pages
        c.execute('''
            CREATE TABLE IF NOT EXISTS pages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                page_id TEXT,
                url TEXT,
                source TEXT DEFAULT 'manual',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        ''')

        # Create groups table for extracted Facebook groups
        c.execute('''
            CREATE TABLE IF NOT EXISTS groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                group_id TEXT,
                url TEXT,
                source TEXT DEFAULT 'manual',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        ''')

        c.execute('''
            CREATE TABLE IF NOT EXISTS posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                content TEXT,
                website_link TEXT,
                image_path TEXT,
                scheduled_time DATETIME,
                account_id INTEGER, -- Consider making this a foreign key to accounts(id)
                status TEXT DEFAULT 'pending', -- Added default
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                post_type TEXT,
                recipe_text TEXT,
                sched_status_check INTEGER DEFAULT 0, -- Consider renaming/rethinking these flags
                sched_comment_check INTEGER DEFAULT 0,
                sched_checked_check INTEGER DEFAULT 0
                -- FOREIGN KEY(account_id) REFERENCES accounts(id) ON DELETE SET NULL -- Example FK
            );
        ''')
        # Add indexes for common query columns
        c.execute("CREATE INDEX IF NOT EXISTS idx_posts_status_time ON posts (status, scheduled_time);")
        c.execute("CREATE INDEX IF NOT EXISTS idx_posts_type ON posts (post_type);")

        c.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL -- Made name unique and not null
            );
        ''')
        c.execute("CREATE INDEX IF NOT EXISTS idx_categories_name ON categories (name);")

        c.execute('''
            CREATE TABLE IF NOT EXISTS schedules (
               id INTEGER PRIMARY KEY AUTOINCREMENT,
               schedule_json TEXT NOT NULL, -- Made json not null
               created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        conn.commit()
        logging.info("Database initialized successfully.")
        return conn, c
    except sqlite3.Error as e:
        logging.critical(f"Database initialization failed: {e}")
        # Show critical error to user
        # Cannot use QMessageBox here as QApplication might not be running yet
        print(f"CRITICAL: Database error - {e}. Application cannot start.", file=sys.stderr)
        sys.exit(1)

# Initialize globals (consider encapsulating in a class/context later if it grows)
conn, c = initialize_database()

# --- Data Classes ---
@dataclass
class PostData:
    id: int = -1
    content: str = ""
    website_link: str | None = None
    image_path: str | None = None # Store as comma-separated string or JSON list? JSON might be better.
    scheduled_time: datetime.datetime | None = None
    account_id: int | None = None
    status: str = "pending"
    created_at: datetime.datetime = field(default_factory=datetime.datetime.now)
    post_type: str = ""
    recipe_text: str | None = None
    # Remove sched_* flags unless their purpose is clear and actively used

    @property
    def display_pid(self) -> str:
        return f"PID{self.id + PID_OFFSET}" if self.id != -1 else "N/A"

    @property
    def image_list(self) -> list[str]:
        if self.image_path:
            return [img.strip() for img in self.image_path.split(',') if img.strip() and os.path.exists(img.strip())]
        return []

@dataclass
class AccountData:
    id: int = -1
    fb_id: str = ""
    full_name: str | None = None
    cookie: str | None = None
    status: str = "live"
    nst_profile_id: str | None = None
    browser_type: str = "chrome"


# --- Utility Functions ---
def clearLayout(layout):
    """Removes all widgets from a layout."""
    if layout is not None:
        while layout.count():
            item = layout.takeAt(0)
            widget = item.widget()
            if widget:
                # Use deleteLater to ensure safe deletion
                widget.deleteLater()
            else:
                # Recursively clear nested layouts
                clearLayout(item.layout())

def get_web_options(headless=True):
    """Configures Chrome options."""
    options = webdriver.ChromeOptions()
    if headless:
        options.add_argument("--headless=new") # Updated headless argument
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox") # Often needed in Linux/Docker
    options.add_argument("--disable-dev-shm-usage") # Overcomes limited resource problems
    options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36") # Common user agent
    options.add_experimental_option('excludeSwitches', ['enable-logging']) # Reduce console noise
    return options

# Removed generate_hour_options (can be done inline or in specific widget)

def create_thumbnail(path, size=DEFAULT_THUMBNAIL_SIZE):
    """Creates a thumbnail QPixmap from an image path."""
    if not path or not os.path.exists(path):
        logging.warning(f"Thumbnail creation skipped: Path invalid or does not exist: {path}")
        return QPixmap() # Return empty QPixmap
    try:
        img = Image.open(path)
        img.thumbnail(size) # PIL thumbnail resizes in place
        # Convert PIL Image to QPixmap
        if img.mode == "RGB":
            r, g, b = img.split()
            img = Image.merge("RGB", (r, g, b))
        elif img.mode == "RGBA":
            r, g, b, a = img.split()
            img = Image.merge("RGBA", (r, g, b, a))
        elif img.mode == "L": # Grayscale
             img = img.convert("RGBA") # Convert to RGBA for QPixmap

        # Check conversion result
        if img.mode not in ("RGB", "RGBA"):
             logging.warning(f"Unsupported image mode '{img.mode}' for QPixmap conversion: {path}. Trying RGB conversion.")
             img = img.convert("RGB")

        if img.mode == "RGB":
             qimage = QtGui.QImage(img.tobytes("raw", "RGB"), img.width, img.height, QtGui.QImage.Format_RGB888)
        elif img.mode == "RGBA":
             qimage = QtGui.QImage(img.tobytes("raw", "RGBA"), img.width, img.height, QtGui.QImage.Format_RGBA8888)
        else: # Should not happen after conversions above
             logging.error(f"Cannot convert image mode {img.mode} to QPixmap: {path}")
             return QPixmap()

        return QPixmap.fromImage(qimage)
    except Exception as e:
        logging.error(f"Error creating thumbnail for {path}: {e}")
        return QPixmap() # Return empty QPixmap on error


def save_cookie_to_file(cookie_data, filename="account_cookie.json"):
    """Saves cookie data (dict) to a JSON file."""
    try:
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(cookie_data, f, indent=2)
        logging.info(f"Cookie data saved to {filename}")
    except IOError as e:
        logging.error(f"Error saving cookie data to {filename}: {e}")
    except Exception as e:
        logging.error(f"Unexpected error saving cookie data: {e}")


def get_global_categories() -> list[str]:
    """Fetches predefined and database categories."""
    db_cats = []
    try:
        with QMutexLocker(db_mutex):
            # Use fetchall() correctly
            rows = c.execute("SELECT name FROM categories ORDER BY name").fetchall()
        db_cats = [row['name'] for row in rows] # Access by column name due to row_factory
    except sqlite3.Error as e:
        logging.error(f"Failed to fetch categories from database: {e}")
    # Combine and remove duplicates, preserving order
    all_cats = list(dict.fromkeys(POST_CATEGORIES + db_cats))
    return all_cats

# --- Selenium Helper Functions (Consider moving to a separate module/class) ---

def selenium_task_runner(target_func, *args, **kwargs):
    """Runs a Selenium task, handles driver setup/teardown."""
    driver = None
    result = None
    error = None
    try:
        logging.debug(f"Starting Selenium task: {target_func.__name__}")
        # Consider using undetected_chromedriver if needed based on your login method
        driver_path = chromedriver_autoinstaller.install()
        service = Service(driver_path)
        options = get_web_options(headless=True) # Use headless by default
        driver = webdriver.Chrome(service=service, options=options)
        driver.implicitly_wait(5) # Small implicit wait

        result = target_func(driver, *args, **kwargs)
        logging.debug(f"Selenium task {target_func.__name__} completed.")

    except Exception as e:
        error = e
        logging.error(f"Error during Selenium task {target_func.__name__}: {e}", exc_info=True)
    finally:
        if driver:
            try:
                driver.quit()
                logging.debug("Selenium driver quit.")
            except Exception as e:
                logging.error(f"Error quitting Selenium driver: {e}")
    return result, error

def _extract_full_name_selenium(driver):
    """Internal helper to extract name within a Selenium session."""
    # Wait for page to load
    time.sleep(3)

    # Method 1: Look for profile name in various selectors
    selectors = [
        "//h1[contains(@class, 'x1heor9g')]",  # New Facebook layout
        "//h1",  # Generic H1
        "//span[contains(@class, 'x1heor9g')]",  # Profile name span
        "//div[contains(@class, 'x1e56ztr')]//span",  # Profile header
        "//div[@data-pagelet='ProfileTilesFeed']//h1",  # Profile tiles
    ]

    for selector in selectors:
        try:
            name_element = WebDriverWait(driver, 3).until(
                EC.presence_of_element_located((By.XPATH, selector))
            )
            name = name_element.text.strip()
            if name and len(name) > 1 and not name.lower() in ['facebook', 'home', 'profile']:
                logging.info(f"Extracted name using selector {selector}: {name}")
                return name
        except Exception:
            continue

    # Method 2: Meta tag og:title
    try:
        meta = driver.find_element(By.XPATH, "//meta[@property='og:title']")
        name = meta.get_attribute("content").strip()
        if name and len(name) > 1:
            # Clean up common Facebook suffixes
            name = name.replace(" | Facebook", "").replace(" - Facebook", "").strip()
            if name and not name.lower() in ['facebook', 'home']:
                logging.info(f"Extracted name using og:title meta tag: {name}")
                return name
    except Exception:
        pass

    # Method 3: Page title
    try:
        title = driver.title.strip()
        if title:
            # Clean up title
            name = title.split("|")[0].split("-")[0].strip()
            name = name.replace("Facebook", "").replace("(", "").replace(")", "").strip()
            if name and len(name) > 1:
                logging.info(f"Extracted name using page title: {name}")
                return name
    except Exception:
        pass

    # Method 4: Look in page source for name patterns
    try:
        page_source = driver.page_source
        import re
        patterns = [
            r'"name":"([^"]+)"',
            r'"displayName":"([^"]+)"',
            r'"title":"([^"]+)"'
        ]

        for pattern in patterns:
            match = re.search(pattern, page_source)
            if match:
                name = match.group(1).strip()
                if name and len(name) > 1 and not name.lower() in ['facebook', 'home', 'profile']:
                    logging.info(f"Extracted name via pattern {pattern}: {name}")
                    return name
    except Exception:
        pass

    logging.warning("Could not extract full name using any method.")
    return "Unknown Name"

def _extract_fb_uid_selenium(driver):
    """Internal helper to extract UID within a Selenium session."""
    import re

    # Wait for page to load
    time.sleep(3)

    # Method 1: Try common meta tag first
    try:
        meta = driver.find_element(By.XPATH, "//meta[@property='al:ios:url']")
        url = meta.get_attribute("content")
        # Expected format like: "fb://profile/1000..." or "fb://page/?id=123..."
        if url and "profile/" in url:
            uid = url.split("profile/")[-1].split("?")[0]
            if uid.isdigit() and len(uid) > 5:
                logging.info(f"Extracted UID via al:ios:url (profile): {uid}")
                return uid
        elif url and "page/?id=" in url:
             uid = url.split("page/?id=")[-1].split("&")[0]
             if uid.isdigit() and len(uid) > 5:
                 logging.info(f"Extracted UID via al:ios:url (page): {uid}")
                 return uid
    except Exception as e:
        logging.debug(f"Could not extract UID via al:ios:url meta tag: {e}")

    # Method 2: Look for profile link with ID in href
    try:
        link = driver.find_element(By.XPATH, "//a[contains(@href,'profile.php?id=')]")
        href = link.get_attribute("href")
        uid = href.split("id=")[-1].split("&")[0]
        if uid.isdigit() and len(uid) > 5:
            logging.info(f"Extracted UID via profile.php link: {uid}")
            return uid
    except Exception as e:
        logging.debug(f"Could not extract UID via profile.php link: {e}")

    # Method 3: Entity ID from page source
    try:
        page_source = driver.page_source
        match = re.search(r'"entity_id":"(\d+)"', page_source)
        if match:
            uid = match.group(1)
            if uid.isdigit() and len(uid) > 5:
                logging.info(f"Extracted UID via entity_id in page source: {uid}")
                return uid
    except Exception as e:
        logging.debug(f"Could not extract UID via entity_id: {e}")

    # Method 4: Look for user ID in various page source patterns
    try:
        page_source = driver.page_source
        patterns = [
            r'"USER_ID":"(\d+)"',
            r'"userID":"(\d+)"',
            r'"actorID":"(\d+)"',
            r'"pageID":"(\d+)"',
            r'c_user=(\d+)',
            r'"id":(\d+),"name"'
        ]

        for pattern in patterns:
            match = re.search(pattern, page_source)
            if match:
                uid = match.group(1)
                if uid.isdigit() and len(uid) > 5:
                    logging.info(f"Extracted UID via pattern {pattern}: {uid}")
                    return uid
    except Exception as e:
        logging.debug(f"Could not extract UID via page source patterns: {e}")

    logging.warning("Could not extract Facebook UID using any method.")
    return "" # Return empty string if not found

# --- NST Browser Integration ---

def get_remote_chrome_version(debugger_address):
    """Query the remote debugging endpoint to retrieve the actual Chrome version."""
    try:
        url = f"http://{debugger_address}/json/version"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        browser_info = data.get("Browser", "")
        import re
        version_match = re.search(r'Chrome/(\d+\.\d+\.\d+\.\d+)', browser_info)
        if version_match:
            version = version_match.group(1)
            logging.info(f"Detected remote Chrome version: {version}")
            return version
        else:
            logging.error("Could not extract Chrome version from remote debugger info.")
    except Exception as e:
        logging.error(f"Error retrieving remote Chrome version: {e}")
    return None

def get_debugger_port(url, max_retries=10, retry_interval=5):
    """Retrieve the debugger port by polling the specified URL."""
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            return data.get('data', {}).get('port')
        except requests.RequestException as e:
            logging.warning(f"Error retrieving debugger port: {e}. Retrying...")
            time.sleep(retry_interval)
    logging.critical("Failed to retrieve debugger port.")
    return None

def launch_and_connect_to_browser(profile_id, max_retries=10, retry_interval=5):
    """Launch browser for the given profile and retrieve debugger address."""
    api_key = 'fc63ee6b-0785-4b2a-a179-d6ae22c88479'
    host = '127.0.0.1'
    config = {'headless': False, 'autoClose': True}
    query = urlencode({'x-api-key': api_key, 'config': quote(json.dumps(config))})
    url = f'http://{host}:8848/devtool/launch/{profile_id}?{query}'

    port = get_debugger_port(url, max_retries, retry_interval)
    if port:
        return f"{host}:{port}"
    return None

def kill_nstchrome_processes():
    """Kill nstchrome.exe processes."""
    try:
        os.system("taskkill /F /IM nstchrome.exe > NUL 2>&1")
        logging.info("Killed nstchrome.exe processes")
    except Exception as e:
        logging.error(f"Error killing nstchrome processes: {e}")

def get_nst_profiles_list():
    """Get list of available NST Browser profiles."""
    try:
        api_key = 'fc63ee6b-0785-4b2a-a179-d6ae22c88479'
        host = '127.0.0.1'
        url = f'http://{host}:8848/devtool/profiles'

        headers = {'x-api-key': api_key}
        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0 and 'data' in data:
                profiles = data['data']
                logging.info(f"Found {len(profiles)} NST Browser profiles")
                return profiles

        logging.warning("Failed to get NST profiles list")
        return []

    except Exception as e:
        logging.error(f"Error getting NST profiles list: {e}")
        return []

def check_nst_profile_status(profile_id):
    """Check if NST Browser profile is running and Facebook is logged in."""
    try:
        # Try to launch and connect
        debugger_address = launch_and_connect_to_browser(profile_id)
        if not debugger_address:
            return {"status": "offline", "facebook_status": "unknown", "error": "Cannot launch browser"}

        # Connect with Selenium
        driver = exec_selenium_nst(debugger_address)
        if not driver:
            return {"status": "online", "facebook_status": "unknown", "error": "Cannot connect Selenium"}

        try:
            # Check Facebook login status
            driver.get("https://www.facebook.com")
            time.sleep(5)

            current_url = driver.current_url.lower()
            page_source = driver.page_source.lower()

            # Check if logged in
            if "login" in current_url or "checkpoint" in current_url:
                fb_status = "not_logged_in"
            elif "facebook.com/checkpoint" in current_url:
                fb_status = "checkpoint"
            elif "your account has been" in page_source or "account disabled" in page_source:
                fb_status = "banned"
            elif "facebook.com" in current_url and ("home" in current_url or "feed" in current_url or len(current_url.split('/')) <= 4):
                fb_status = "active"
            else:
                fb_status = "unknown"

            return {
                "status": "online",
                "facebook_status": fb_status,
                "url": driver.current_url,
                "title": driver.title
            }

        finally:
            try:
                driver.quit()
            except:
                pass

    except Exception as e:
        logging.error(f"Error checking NST profile {profile_id}: {e}")
        return {"status": "error", "facebook_status": "unknown", "error": str(e)}

def extract_facebook_pages_and_groups(profile_id):
    """Extract Facebook pages and groups managed by the account."""
    try:
        # Launch browser
        debugger_address = launch_and_connect_to_browser(profile_id)
        if not debugger_address:
            raise Exception("Cannot launch NST Browser")

        driver = exec_selenium_nst(debugger_address)
        if not driver:
            raise Exception("Cannot connect Selenium")

        pages = []
        groups = []

        try:
            # Extract Pages
            logging.info("Extracting Facebook Pages...")
            driver.get("https://www.facebook.com/pages/?category=your_pages")
            time.sleep(5)

            # Try different selectors for pages
            page_selectors = [
                "//a[contains(@href, '/pages/')]",
                "//div[contains(@data-pagelet, 'Pages')]//a",
                "//div[contains(@aria-label, 'Page')]//a"
            ]

            for selector in page_selectors:
                try:
                    page_elements = driver.find_elements(By.XPATH, selector)
                    for element in page_elements:
                        try:
                            href = element.get_attribute('href')
                            text = element.text.strip()
                            if href and 'facebook.com' in href and text and len(text) > 1:
                                # Extract page ID from URL
                                page_id = None
                                if '/pages/' in href:
                                    page_id = href.split('/pages/')[-1].split('/')[0]
                                elif 'page_id=' in href:
                                    page_id = href.split('page_id=')[-1].split('&')[0]

                                page_info = {
                                    'name': text,
                                    'url': href,
                                    'page_id': page_id,
                                    'type': 'page'
                                }
                                if page_info not in pages:
                                    pages.append(page_info)
                        except:
                            continue
                    if pages:
                        break
                except:
                    continue

            # Extract Groups
            logging.info("Extracting Facebook Groups...")
            driver.get("https://www.facebook.com/groups/feed/")
            time.sleep(5)

            # Try different selectors for groups
            group_selectors = [
                "//a[contains(@href, '/groups/')]",
                "//div[contains(@data-pagelet, 'Groups')]//a",
                "//div[contains(@aria-label, 'Group')]//a"
            ]

            for selector in group_selectors:
                try:
                    group_elements = driver.find_elements(By.XPATH, selector)
                    for element in group_elements:
                        try:
                            href = element.get_attribute('href')
                            text = element.text.strip()
                            if href and '/groups/' in href and text and len(text) > 1:
                                # Extract group ID from URL
                                group_id = None
                                if '/groups/' in href:
                                    group_id = href.split('/groups/')[-1].split('/')[0]

                                group_info = {
                                    'name': text,
                                    'url': href,
                                    'group_id': group_id,
                                    'type': 'group'
                                }
                                if group_info not in groups:
                                    groups.append(group_info)
                        except:
                            continue
                    if groups:
                        break
                except:
                    continue

            return {
                'success': True,
                'pages': pages,
                'groups': groups,
                'profile_id': profile_id
            }

        finally:
            try:
                driver.quit()
            except:
                pass

    except Exception as e:
        logging.error(f"Error extracting pages/groups for profile {profile_id}: {e}")
        return {
            'success': False,
            'error': str(e),
            'pages': [],
            'groups': [],
            'profile_id': profile_id
        }

def exec_selenium_nst(debugger_address):
    """Start Selenium WebDriver using the debugger address."""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager

        options = Options()
        options.debugger_address = debugger_address

        # Try to get the remote Chrome version for better compatibility
        remote_version = get_remote_chrome_version(debugger_address)
        if remote_version:
            service = Service(ChromeDriverManager(driver_version=remote_version).install())
        else:
            service = Service(ChromeDriverManager().install())

        driver = webdriver.Chrome(service=service, options=options)
        logging.info(f"Connected to NST Browser at {debugger_address}")
        return driver
    except Exception as e:
        logging.error(f"Failed to initialize WebDriver: {e}")
        return None

def exec_selenium_nst_headless(debugger_address):
    """Start Selenium WebDriver using the debugger address in headless mode."""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager

        options = Options()
        options.debugger_address = debugger_address

        # Add headless options for better performance
        options.add_argument('--headless=new')  # Use new headless mode
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images')  # Don't load images for faster processing
        options.add_argument('--disable-javascript')  # Disable JS for faster loading

        # Try to get the remote Chrome version for better compatibility
        remote_version = get_remote_chrome_version(debugger_address)
        if remote_version:
            service = Service(ChromeDriverManager(driver_version=remote_version).install())
        else:
            service = Service(ChromeDriverManager().install())

        driver = webdriver.Chrome(service=service, options=options)
        logging.info(f"Connected to NST Browser at {debugger_address} (headless mode)")
        return driver
    except Exception as e:
        logging.error(f"Failed to initialize headless WebDriver: {e}")
        return None

def create_driver_for_account(account: AccountData):
    """Create NST Browser driver for account."""
    if not account.nst_profile_id:
        raise Exception(f"Account {account.full_name} does not have NST Profile ID configured")

    # Use NST Browser only
    kill_nstchrome_processes()
    time.sleep(2)

    debugger_address = launch_and_connect_to_browser(account.nst_profile_id)
    if not debugger_address:
        raise Exception(f"Failed to launch NST Browser for profile {account.nst_profile_id}")

    driver = exec_selenium_nst(debugger_address)
    if not driver:
        raise Exception(f"Failed to connect to NST Browser for profile {account.nst_profile_id}")

    logging.info(f"Created NST Browser driver for account {account.full_name}")
    return driver

def _load_cookies_and_navigate(driver, cookie_str, url):
    """Loads cookies into the driver and navigates to a URL."""
    driver.get("https://www.facebook.com") # Go to domain first to set cookies
    time.sleep(1) # Small delay
    # Improved cookie loading
    cookies_loaded_count = 0
    try:
        # Try parsing as JSON first (more robust if saved correctly)
        try:
            cookies_list = json.loads(cookie_str)
            for cookie in cookies_list:
                # Ensure essential keys are present
                if 'name' in cookie and 'value' in cookie:
                    # Selenium expects domain, path etc. Add defaults if missing.
                    cookie.setdefault('domain', '.facebook.com')
                    cookie.setdefault('path', '/')
                    # Remove problematic keys if they exist
                    cookie.pop('expiry', None)
                    cookie.pop('httpOnly', None) # Sometimes causes issues
                    cookie.pop('sameSite', None) # Can be problematic

                    try:
                        driver.add_cookie(cookie)
                        cookies_loaded_count += 1
                    except Exception as e:
                        logging.warning(f"Failed to add cookie (JSON): {cookie.get('name', 'N/A')} - {e}")
        except json.JSONDecodeError:
            logging.debug("Cookie string is not JSON, trying key:value format.")
            # Fallback to key:value parsing
            for line in cookie_str.splitlines():
                if ":" in line:
                    name, value = line.strip().split(":", 1)
                    name = name.strip()
                    value = value.strip()
                    if name and value:
                        try:
                            driver.add_cookie({"name": name, "value": value, "domain": ".facebook.com", "path": "/"})
                            cookies_loaded_count += 1
                        except Exception as e:
                            logging.warning(f"Failed to add cookie (key:value): {name} - {e}")

        logging.debug(f"Loaded {cookies_loaded_count} cookies.")
        if cookies_loaded_count == 0:
             raise ValueError("No valid cookies could be loaded.")

        driver.get(url)
        time.sleep(3) # Allow page to load after cookie injection + navigation
        return True
    except Exception as e:
        logging.error(f"Failed to load cookies or navigate: {e}", exc_info=True)
        return False

def _fetch_pages_selenium(driver, cookie_str):
    """Fetches pages managed by the account."""
    if not _load_cookies_and_navigate(driver, cookie_str, "https://www.facebook.com/pages/?category=your_pages"):
        return [], ValueError("Failed to load cookies or navigate to pages.")
    
    pages = []
    try:
        # Scroll down to load all pages (might need adjustments)
        last_height = driver.execute_script("return document.body.scrollHeight")
        for _ in range(3): # Scroll a few times
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height

        # Use more specific selectors if possible (inspect FB page structure)
        # This is a generic example, likely needs updating based on current FB structure
        page_elements = driver.find_elements(By.XPATH, "//a[contains(@href, '/pages/')]") # Very generic

        # Refined script to extract data (adjust selectors based on FB structure)
        pages = driver.execute_script("""
            var pagesList = [];
            // Example: Find elements containing page links and names. Adjust selector!
            var elems = document.querySelectorAll('div[role="presentation"] a[href*="/pages/"]');
            elems.forEach(el => {
                var nameElement = el.querySelector('span'); // Adjust if name is elsewhere
                var name = nameElement ? nameElement.innerText.trim() : el.innerText.trim(); // Get name
                var href = el.href;
                if (name && href) {
                    var idMatch = href.match(/pages\\/(\\d+)/) || href.match(/\\/(\\d+)(\\/?$|\\/\\?)/); // Try different ID patterns
                    var id = idMatch ? idMatch[1] : "";
                    // Basic filtering to avoid irrelevant links
                    if (id && name.length > 1 && name !== 'See More') {
                        pagesList.push({name: name, id: id, link: href});
                    }
                }
            });
            // Remove duplicates based on ID
            const uniquePages = Array.from(new Map(pagesList.map(p => [p.id, p])).values());
            return uniquePages;
        """)
        logging.info(f"Fetched {len(pages)} unique pages.")
    except Exception as e:
        logging.error(f"Error fetching pages with Selenium: {e}", exc_info=True)
        return [], e # Return error object
        
    return pages, None # Return pages and no error

def _fetch_groups_selenium(driver, cookie_str):
    """Fetches groups the account is in."""
    if not _load_cookies_and_navigate(driver, cookie_str, "https://www.facebook.com/groups/feed/"): # Go to groups feed
        return [], ValueError("Failed to load cookies or navigate to groups.")

    groups = []
    try:
         # Scroll down to load groups (might need adjustments)
        last_height = driver.execute_script("return document.body.scrollHeight")
        for _ in range(5): # Scroll more for groups potentially
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2.5)
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height

        # Script to extract group data (adjust selectors based on FB structure)
        groups = driver.execute_script("""
            var groupsList = [];
            // Example: Look for links within a specific group list container. Adjust selector!
            var elems = document.querySelectorAll('a[href*="/groups/"]');
            elems.forEach(el => {
                var nameElement = el.querySelector('span'); // Adjust selector
                var name = nameElement ? nameElement.innerText.trim() : el.innerText.trim();
                var href = el.href;
                if (name && href && href.includes('/groups/')) {
                    // Extract ID: digits between /groups/ and next / or end of string
                    var idMatch = href.match(/\\/groups\\/(\\d+)/);
                    var id = idMatch ? idMatch[1] : "";
                    // Filter out common irrelevant links and ensure ID looks valid
                    if (id && name.length > 1 && !href.includes('/feed/') && !href.includes('/membership/') && !href.includes('/join/')) {
                        groupsList.push({name: name, id: id, link: href});
                    }
                }
            });
            // Remove duplicates based on ID
            const uniqueGroups = Array.from(new Map(groupsList.map(g => [g.id, g])).values());
            return uniqueGroups;
        """)
        logging.info(f"Fetched {len(groups)} unique groups.")
    except Exception as e:
        logging.error(f"Error fetching groups with Selenium: {e}", exc_info=True)
        return [], e

    return groups, None

# --- Worker Threads ---

class BaseWorker(QThread):
    """Base worker thread for handling signals."""
    finished = pyqtSignal(object, object) # result, error
    progress = pyqtSignal(str) # status message

    def __init__(self, target_func, *args, **kwargs):
        super().__init__()
        self._target_func = target_func
        self._args = args
        self._kwargs = kwargs

    def run(self):
        result = None
        error = None
        try:
            self.progress.emit(f"Starting: {self._target_func.__name__}...")
            # For Selenium tasks, use the runner
            if "selenium" in self._target_func.__name__:
                 result, error = selenium_task_runner(self._target_func, *self._args, **self._kwargs)
            else:
                 # For non-Selenium tasks (e.g., database checks)
                 result = self._target_func(*self._args, **self._kwargs)
            if error:
                 self.progress.emit(f"Error in {self._target_func.__name__}: {error}")
            else:
                 self.progress.emit(f"Finished: {self._target_func.__name__}.")
        except Exception as e:
            error = e
            logging.error(f"Unexpected error in worker {self._target_func.__name__}: {e}", exc_info=True)
            self.progress.emit(f"Critical error in worker: {e}")
        finally:
            self.finished.emit(result, error)


class CheckPostsWorker(QThread):
    """Background worker to check for posts to mark as 'posted'."""
    postsStatusChanged = pyqtSignal(list) # Emit list of updated post IDs
    errorOccurred = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self._running = True
        # Each worker thread needs its own DB connection for safety with check_same_thread=False
        self.local_conn = None
        self.local_cursor = None

    def run(self):
        try:
            self.local_conn = sqlite3.connect(DB_FILE, check_same_thread=False)
            self.local_conn.row_factory = sqlite3.Row
            self.local_cursor = self.local_conn.cursor()
            logging.info("CheckPostsWorker started with new DB connection.")
        except sqlite3.Error as e:
            logging.critical(f"CheckPostsWorker failed to connect to DB: {e}")
            self.errorOccurred.emit(f"Database connection error: {e}")
            self._running = False # Stop the thread if DB fails

        while self._running:
            updated_post_ids = []
            try:
                # Use CURRENT_TIMESTAMP directly in SQL for better accuracy
                # Find posts scheduled for the past, status 'pending'
                # Use strftime for comparison as DATETIME might be stored as text
                # Corrected query: Compare scheduled_time (assumed stored as TEXT ISO format or UNIX timestamp)
                # If stored as ISO string:
                # query = "SELECT id FROM posts WHERE status='pending' AND scheduled_time <= strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')"
                # If stored as UNIX timestamp (more reliable):
                now_ts = time.time()
                query = "SELECT id FROM posts WHERE status='pending' AND scheduled_time <= ?"

                pending_posts = self.local_cursor.execute(query, (now_ts,)).fetchall()

                if pending_posts:
                    logging.debug(f"Found {len(pending_posts)} pending posts to update.")
                    for row in pending_posts:
                        post_id = row['id']
                        try:
                            # Update status to 'posted'
                            self.local_cursor.execute("UPDATE posts SET status='posted' WHERE id=?", (post_id,))
                            updated_post_ids.append(post_id)
                            logging.debug(f"Marked post ID {post_id} as 'posted'.")
                        except sqlite3.Error as update_err:
                            logging.exception(f"Failed to update post ID {post_id} status to 'posted': {update_err}")
                            # Optionally update to 'failed' state
                            # self.local_cursor.execute("UPDATE posts SET status='failed' WHERE id=?", (post_id,))
                    if updated_post_ids:
                         self.local_conn.commit() # Commit after processing batch
                         self.postsStatusChanged.emit(updated_post_ids) # Emit IDs that changed

            except sqlite3.Error as e:
                logging.error(f"CheckPostsWorker database error: {e}")
                self.errorOccurred.emit(f"Database check error: {e}")
                # Avoid busy-waiting on critical DB error
                self.msleep(15000) # Wait longer before retrying
            except Exception as e:
                logging.error(f"CheckPostsWorker unexpected error: {e}", exc_info=True)
                self.errorOccurred.emit(f"Unexpected worker error: {e}")
                self.msleep(15000)

            # Sleep for a while before checking again
            # Check every 30 seconds, check running flag frequently within sleep
            for _ in range(300): # 300 * 100ms = 30 seconds
                if not self._running:
                    break
                self.msleep(100)

        # Cleanup connection when thread stops
        if self.local_conn:
            try:
                self.local_conn.close()
                logging.info("CheckPostsWorker database connection closed.")
            except sqlite3.Error as e:
                logging.error(f"Error closing CheckPostsWorker DB connection: {e}")

    def stop(self):
        """Signals the worker thread to stop."""
        logging.info("Stopping CheckPostsWorker...")
        self._running = False
        # No need to call quit() or wait() here if the loop checks _running frequently


# --- Custom Widgets ---

# HoursCalendarWidget: Seems reasonable, keep as is or slightly restyle.
class HoursCalendarWidget(QWidget):
    # (Keep existing implementation - maybe add minor styling)
    def __init__(self, parent=None, initial_selected_hours=None):
        super().__init__(parent)
        self.selected_hours = set(initial_selected_hours) if initial_selected_hours else set()
        # Generate hours dynamically
        self.hours = [datetime.time(h).strftime("%I:%M %p") for h in range(24)] # Simpler generation

        self.layout = QGridLayout(self)
        self.layout.setSpacing(4) # Tighter spacing
        self.layout.setContentsMargins(0, 0, 0, 0)
        col_count = 4

        for index, hour in enumerate(self.hours):
            btn = QPushButton(hour)
            btn.setCheckable(True)
            btn.setMinimumWidth(60) # Ensure buttons are wide enough
            # Use stylesheet from central theme later, but basic style here
            btn.setStyleSheet("""
                QPushButton {
                    border: 1px solid #CCCCCC;
                    border-radius: 3px;
                    padding: 5px;
                    background-color: #FFFFFF; /* Default */
                    color: #333333;
                }
                QPushButton:checked {
                    background-color: #A0D2EB; /* Softer blue */
                    border: 1px solid #7AB8E4;
                    color: #000000;
                }
                QPushButton:hover {
                    background-color: #EFEFEF;
                }
                QPushButton:checked:hover {
                    background-color: #8AC6E6;
                }
            """)
            if hour in self.selected_hours:
                btn.setChecked(True)
            btn.toggled.connect(lambda checked, h=hour: self.on_button_toggled(checked, h))

            row = index // col_count
            col = index % col_count
            self.layout.addWidget(btn, row, col)

    def on_button_toggled(self, checked, hour):
        if checked:
            self.selected_hours.add(hour)
        else:
            self.selected_hours.discard(hour)
        # Optional: Emit a signal when selection changes
        # self.selectionChanged.emit(self.getSelectedHours())

    def getSelectedHours(self):
        return sorted(list(self.selected_hours), key=lambda x: datetime.datetime.strptime(x, "%I:%M %p").time())

# ScheduleTimeItemWidget: Looks functional, add minor styling.
class ScheduleTimeItemWidget(QWidget):
    removed = pyqtSignal(QWidget) # Signal to notify parent when removed

    def __init__(self, parent=None):
        super().__init__(parent)
        # Use a frame-like style for better visual grouping
        self.setStyleSheet("""
            ScheduleTimeItemWidget {
                background-color: #FDFDFD;
                border: 1px solid #E0E0E0;
                padding: 8px;
                border-radius: 4px;
                margin-bottom: 4px; /* Add space between items */
            }
            QComboBox { padding: 3px; }
            QPushButton { padding: 4px 8px; }
        """)
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(6)

        self.hourCombo = QComboBox()
        self.hourCombo.addItems([f"{h:02d}" for h in range(1, 13)])
        layout.addWidget(self.hourCombo)

        self.minuteCombo = QComboBox()
        self.minuteCombo.addItems([f"{m:02d}" for m in range(0, 60, 5)]) # Steps of 5min default
        self.minuteCombo.setEditable(True) # Allow custom minutes
        self.minuteCombo.lineEdit().setValidator(QtGui.QIntValidator(0, 59)) # Validate input
        layout.addWidget(self.minuteCombo)

        self.ampmCombo = QComboBox()
        self.ampmCombo.addItems(["AM", "PM"])
        layout.addWidget(self.ampmCombo)

        self.categoryCombo = QComboBox()
        self.categoryCombo.addItems(get_global_categories()) # Fetch categories dynamically
        layout.addWidget(self.categoryCombo)
        layout.addStretch(1) # Push button to the right

        self.removeButton = QPushButton(QIcon.fromTheme("edit-delete", QIcon(":/qt-project.org/styles/commonstyle/images/standardbutton-cancel-16.png")), "") # Use icon
        self.removeButton.setToolTip("Remove this time slot")
        self.removeButton.setFlat(True) # Make it look less intrusive
        self.removeButton.setFixedSize(QSize(24, 24)) # Small square button
        self.removeButton.setStyleSheet("QPushButton { border: none; background-color: transparent; } QPushButton:hover { background-color: #FFEBEE; }") # Minimal style
        self.removeButton.setCursor(Qt.PointingHandCursor)

        layout.addWidget(self.removeButton)
        self.removeButton.clicked.connect(self.removeSelf)

    def removeSelf(self):
        self.removed.emit(self) # Emit signal before deletion
        self.deleteLater()

    def getData(self):
        try:
            hour = int(self.hourCombo.currentText())
            minute_text = self.minuteCombo.currentText()
            minute = int(minute_text) if minute_text.isdigit() else 0 # Default to 0 if invalid
            ampm = self.ampmCombo.currentText()

            # Ensure valid time components
            if not (1 <= hour <= 12 and 0 <= minute <= 59):
                raise ValueError("Invalid hour or minute")

            time_str = f"{hour:02d}:{minute:02d} {ampm}"
            category = self.categoryCombo.currentText()
            return {"time": time_str, "category": category}
        except ValueError as e:
            logging.warning(f"Invalid time data entered in ScheduleTimeItemWidget: {e}")
            QMessageBox.warning(self, "Invalid Time", "Please enter a valid time (HH:MM AM/PM).")
            return None # Indicate error

    def setData(self, time_str, category):
        """Sets the widget's values from data."""
        try:
            dt_obj = datetime.datetime.strptime(time_str, "%I:%M %p")
            self.hourCombo.setCurrentText(dt_obj.strftime("%I")) # %I is 12-hour 01-12
            self.minuteCombo.setCurrentText(dt_obj.strftime("%M"))
            self.ampmCombo.setCurrentText(dt_obj.strftime("%p"))
            self.categoryCombo.setCurrentText(category)
        except ValueError:
            logging.warning(f"Could not parse time string '{time_str}' to set ScheduleTimeItemWidget data.")

# StartDateDialog: Looks good.
class StartDateDialog(QDialog):
    # (Keep existing implementation - maybe minor styling)
     def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Select Schedule Start Month")
        # Apply some basic styling
        self.setStyleSheet("QDialog { background-color: #F5F5F5; } QPushButton { padding: 6px 12px; }")

        layout = QFormLayout(self)
        layout.setSpacing(10)

        self.monthCombo = QComboBox()
        months = [datetime.date(2000, m, 1).strftime('%B') for m in range(1, 13)]
        self.monthCombo.addItems(months)
        # Default to current month
        self.monthCombo.setCurrentIndex(datetime.date.today().month - 1)
        layout.addRow("Start Month:", self.monthCombo)

        self.yearSpin = QSpinBox()
        current_year = datetime.date.today().year
        self.yearSpin.setMinimum(current_year)
        self.yearSpin.setMaximum(current_year + 5) # Limit future years reasonably
        self.yearSpin.setValue(current_year)
        layout.addRow("Start Year:", self.yearSpin)

        # Standard OK/Cancel buttons
        buttonLayout = QHBoxLayout()
        self.okButton = QPushButton("OK")
        self.okButton.setDefault(True) # Allow Enter key
        self.okButton.setCursor(Qt.PointingHandCursor)
        self.okButton.clicked.connect(self.accept)

        self.cancelButton = QPushButton("Cancel")
        self.cancelButton.setCursor(Qt.PointingHandCursor)
        self.cancelButton.clicked.connect(self.reject)

        buttonLayout.addStretch()
        buttonLayout.addWidget(self.okButton)
        buttonLayout.addWidget(self.cancelButton)
        layout.addRow(buttonLayout) # Add row for buttons

     def getStartDate(self):
        """Returns the first day of the selected month and year."""
        month_index = self.monthCombo.currentIndex() + 1
        year = self.yearSpin.value()
        try:
            return datetime.date(year, month_index, 1)
        except ValueError:
            # Should not happen with combo/spinbox, but good practice
            logging.error(f"Invalid date created in StartDateDialog: {year}-{month_index}-01")
            # Fallback to current date
            return datetime.date.today().replace(day=1)

# CreateScheduleDialog: Refine UI and logic. Fetch posts more efficiently.
class CreateScheduleDialog(QDialog):
    scheduleCreated = pyqtSignal(list) # Emit the created schedule list

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Create Monthly Schedule")
        # Consistent styling
        self.setStyleSheet("""
            QDialog { background-color: #FFFFFF; font-size: 11pt; }
            QLabel { margin-bottom: 4px; }
            QPushButton { padding: 8px 15px; margin-top: 10px; }
            QScrollArea { border: 1px solid #DCDCDC; }
            QSpinBox, QComboBox, QLineEdit { padding: 4px; }
        """)
        self.resize(650, 600) # Adjust size

        self.schedule_items = [] # To hold the ScheduleTimeItemWidget instances

        mainLayout = QVBoxLayout(self)
        mainLayout.setSpacing(15)

        # --- Top Settings ---
        settingsLayout = QGridLayout()
        settingsLayout.setSpacing(10)

        # Type (Profile/Page/Group)
        settingsLayout.addWidget(QLabel("Schedule Target Type:"), 0, 0)
        self.typeCombo = QComboBox()
        self.typeCombo.addItems(["Profile", "Page", "Group"])
        self.typeCombo.currentIndexChanged.connect(self.update_target_name_placeholder)
        settingsLayout.addWidget(self.typeCombo, 0, 1)

        # Target Name (Profile Name, Page Name, Group Name)
        settingsLayout.addWidget(QLabel("Target Name:"), 1, 0)
        self.targetNameEdit = QLineEdit()
        self.update_target_name_placeholder() # Set initial placeholder
        settingsLayout.addWidget(self.targetNameEdit, 1, 1)

        # Posts per Day
        settingsLayout.addWidget(QLabel("Posts per Day:"), 0, 2)
        self.postsPerDaySpin = QSpinBox()
        self.postsPerDaySpin.setMinimum(1)
        self.postsPerDaySpin.setMaximum(24) # Sensible maximum
        self.postsPerDaySpin.setValue(1)
        self.postsPerDaySpin.valueChanged.connect(self.update_schedule_item_widgets)
        settingsLayout.addWidget(self.postsPerDaySpin, 0, 3)

        mainLayout.addLayout(settingsLayout)

        # --- Schedule Items ---
        mainLayout.addWidget(QLabel("Define daily post slots (time and category):"))

        self.itemsContainerWidget = QWidget()
        self.itemsContainerLayout = QVBoxLayout(self.itemsContainerWidget)
        self.itemsContainerLayout.setContentsMargins(5, 5, 5, 5)
        self.itemsContainerLayout.setSpacing(5)
        # self.itemsContainerLayout.addStretch(1) # Add stretch at the bottom

        itemsScroll = QScrollArea()
        itemsScroll.setWidgetResizable(True)
        itemsScroll.setWidget(self.itemsContainerWidget)
        itemsScroll.setMinimumHeight(200) # Ensure it has some height
        mainLayout.addWidget(itemsScroll)

        # Initial population of schedule items
        self.update_schedule_item_widgets(self.postsPerDaySpin.value())

        # --- Buttons ---
        buttonLayout = QHBoxLayout()
        self.confirmBtn = QPushButton(QIcon.fromTheme("dialog-ok-apply"), "Generate Schedule")
        self.confirmBtn.setCursor(Qt.PointingHandCursor)
        # self.confirmBtn.setStyleSheet("background-color: #A0D2EB;")
        self.confirmBtn.clicked.connect(self.confirm_and_generate_schedule)
        self.confirmBtn.setDefault(True)

        self.cancelBtn = QPushButton(QIcon.fromTheme("dialog-cancel"), "Cancel")
        self.cancelBtn.setCursor(Qt.PointingHandCursor)
        # self.cancelBtn.setStyleSheet("background-color: #E0E0E0;")
        self.cancelBtn.clicked.connect(self.reject)

        buttonLayout.addStretch()
        buttonLayout.addWidget(self.confirmBtn)
        buttonLayout.addWidget(self.cancelBtn)
        mainLayout.addLayout(buttonLayout)

    def update_target_name_placeholder(self):
        target_type = self.typeCombo.currentText()
        placeholders = {
            "Profile": "Enter Profile Name or ID",
            "Page": "Enter Page Name or ID",
            "Group": "Enter Group Name or ID"
        }
        self.targetNameEdit.setPlaceholderText(placeholders.get(target_type, "Enter Target Name/ID"))

    def update_schedule_item_widgets(self, count):
        """Add or remove ScheduleTimeItemWidget instances."""
        current_count = len(self.schedule_items)

        # Add widgets if needed
        if count > current_count:
            for _ in range(count - current_count):
                widget = ScheduleTimeItemWidget(self.itemsContainerWidget)
                widget.removed.connect(self.handle_item_removed) # Connect removal signal
                # Insert before the stretch if you have one
                self.itemsContainerLayout.insertWidget(self.itemsContainerLayout.count(), widget) # Add at the end
                self.schedule_items.append(widget)
        # Remove widgets if needed
        elif count < current_count:
            for _ in range(current_count - count):
                if self.schedule_items:
                    widget_to_remove = self.schedule_items.pop()
                    widget_to_remove.removed.disconnect(self.handle_item_removed) # Disconnect signal
                    widget_to_remove.deleteLater()

        # Adjust spinbox if removal happened via button
        if count != self.postsPerDaySpin.value():
             self.postsPerDaySpin.setValue(count)

    def handle_item_removed(self, widget):
        """Handle removal triggered by the item's own remove button."""
        if widget in self.schedule_items:
            self.schedule_items.remove(widget)
            # Update the spinbox to reflect the new count
            self.postsPerDaySpin.setValue(len(self.schedule_items))

    def confirm_and_generate_schedule(self):
        target_name = self.targetNameEdit.text().strip()
        target_type = self.typeCombo.currentText() # Get selected type

        if not target_name:
            QMessageBox.warning(self, "Input Required", f"Please enter the {target_type} Name or ID.")
            self.targetNameEdit.setFocus()
            return

        schedule_definitions = []
        valid_definitions = True
        for item_widget in self.schedule_items:
            data = item_widget.getData()
            if data:
                schedule_definitions.append(data)
            else:
                valid_definitions = False # Found an invalid time entry
                break # Stop processing if one is invalid

        if not valid_definitions:
             QMessageBox.warning(self, "Invalid Time", "One or more time slots have invalid times. Please correct them.")
             return

        if not schedule_definitions:
            QMessageBox.warning(self, "Input Required", "Please define at least one valid post time slot.")
            return

        # Get start date
        startDateDialog = StartDateDialog(self)
        if startDateDialog.exec_() != QDialog.Accepted:
            return # User cancelled date selection
        start_date = startDateDialog.getStartDate()

        # Fetch available posts for each required category efficiently
        required_categories = list(set(d['category'] for d in schedule_definitions))
        available_posts = {}
        try:
            with QMutexLocker(db_mutex):
                for category in required_categories:
                    # Fetch only necessary data (id, content, image_path)
                    rows = c.execute(
                        "SELECT id, content, image_path FROM posts WHERE post_type=? AND status != 'failed' ORDER BY RANDOM()", # Randomize selection
                        (category,)
                    ).fetchall()
                    if rows:
                        available_posts[category] = [
                            {"post_id": row['id'], "content": row['content'], "image_path": row['image_path']}
                            for row in rows
                        ]
                    else:
                         available_posts[category] = []
                         logging.warning(f"No available posts found for category: {category}")

        except sqlite3.Error as e:
            logging.error(f"Database error fetching posts for schedule generation: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not fetch posts: {e}")
            return

        # Check if any category has no posts
        missing_posts = [cat for cat in required_categories if not available_posts.get(cat)]
        if missing_posts:
            QMessageBox.warning(self, "Missing Posts",
                                f"No posts found for the following categories: {', '.join(missing_posts)}. "
                                "Schedule generation aborted.")
            return

        # Generate the schedule entries
        generated_schedule = []
        # Use a copy of the lists to allow reusing posts if needed, or track usage for uniqueness
        post_pools = {cat: list(posts) for cat, posts in available_posts.items()}
        post_indices = {cat: 0 for cat in post_pools}

        num_days = 30 # Or determine based on month length: calendar.monthrange(start_date.year, start_date.month)[1]

        progress = QProgressDialog("Generating schedule...", "Cancel", 0, num_days * len(schedule_definitions), self)
        progress.setWindowModality(Qt.WindowModal)
        progress.setWindowTitle("Schedule Generation")
        progress.setValue(0)
        QtWidgets.QApplication.processEvents() # Show the dialog

        schedule_possible = True
        current_progress = 0

        for day_offset in range(num_days):
            current_date = start_date + datetime.timedelta(days=day_offset)
            # Sort definitions by time for chronological order if needed
            # sorted_definitions = sorted(schedule_definitions, key=lambda d: datetime.datetime.strptime(d['time'], "%I:%M %p").time())

            for definition in schedule_definitions: # Use original order or sorted
                if progress.wasCanceled():
                     schedule_possible = False
                     break

                category = definition['category']
                time_str = definition['time']

                # Select a post for this slot
                if category in post_pools and post_pools[category]:
                    pool = post_pools[category]
                    index = post_indices[category]

                    # Cycle through posts for the category
                    if index >= len(pool):
                        index = 0 # Reset index to reuse posts

                    selected_db_post = pool[index]
                    post_indices[category] = index + 1 # Move to next post for this category

                    # Create the schedule entry dictionary
                    schedule_entry = {
                        "date": current_date.isoformat(),
                        "time": time_str,
                        "item_name": target_name,
                        "target_type": target_type, # Add target type
                        "category": category, # Use the category from the definition
                        "checked": False, # Default checked state
                        # Use the actual DB post ID, content, and images
                        "db_post_id": selected_db_post["post_id"],
                        "post_id": f"PID{selected_db_post['post_id'] + PID_OFFSET}", # Keep display ID
                        "post_content": selected_db_post["content"],
                        "post_images": selected_db_post["image_path"]
                    }
                    generated_schedule.append(schedule_entry)
                else:
                    # This case should be prevented by the check above, but handle defensively
                    logging.error(f"Attempted to schedule for category '{category}' but no posts available or pool empty.")
                    # Decide behaviour: skip slot, abort, etc.
                    # For now, just log and continue, the schedule will be incomplete.
                    pass # Or set schedule_possible = False

                current_progress += 1
                progress.setValue(current_progress)
                QtWidgets.QApplication.processEvents() # Keep UI responsive

            if not schedule_possible: # Break outer loop if cancelled
                 break

        progress.close() # Close progress dialog

        if not schedule_possible:
             logging.info("Schedule generation cancelled by user.")
             return # Don't proceed if cancelled

        if not generated_schedule:
            QMessageBox.information(self, "Schedule Not Created",
                                    "No schedule entries were generated. This might be due to missing posts or cancellation.")
            return

        logging.info(f"Generated {len(generated_schedule)} schedule entries.")
        self.scheduleCreated.emit(generated_schedule) # Emit the result
        self.accept() # Close the dialog


# --- Table Widgets & Headers ---

# CheckBoxHeader class removed - now using standard header with separate Select All/Deselect All buttons


class PostsTableWidget(QTableWidget):
    """Specialized table for displaying posts with checkboxes and thumbnails."""
    # Define signals if needed, e.g., editRequest = pyqtSignal(int)

    # Define column indices as constants for clarity
    COL_PID = 0
    # Columns vary by type, handle dynamically or use a config

    def __init__(self, post_type, parent=None):
        super().__init__(parent)
        self.post_type = post_type
        self.post_columns = self._get_columns_for_type(post_type)

        self.setColumnCount(len(self.post_columns))
        self.setHorizontalHeaderLabels([col['label'] for col in self.post_columns])

        # Use standard header without checkbox
        header = self.horizontalHeader()
        header.setSectionsClickable(True)

        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.SingleSelection) # Or ExtendedSelection if multi-edit is needed
        self.setEditTriggers(QAbstractItemView.NoEditTriggers) # Don't allow editing directly in table
        self.setAlternatingRowColors(True) # Improve readability
        self.setSortingEnabled(True) # Allow sorting
        self.verticalHeader().setVisible(False) # Hide row numbers

        # Optimize column resizing
        header = self.horizontalHeader()
        # Stretch most columns, but give specific columns fixed/interactive widths
        for i, col_info in enumerate(self.post_columns):
             resize_mode = col_info.get('resize', QHeaderView.Stretch)
             header.setSectionResizeMode(i, resize_mode)
             if 'width' in col_info:
                 header.resizeSection(i, col_info['width']) # Set initial width

        # Connect double click for editing (optional)
        # self.itemDoubleClicked.connect(self.handle_double_click)

    def _get_columns_for_type(self, post_type):
        # Define column structure, labels, resizing, widths, data keys
        # Last column is the 'Select' checkbox column
        common_cols_start = [
            {'label': "Post ID", 'key': 'display_pid', 'resize': QHeaderView.Interactive, 'width': 100},
        ]
        common_cols_end = [
            {'label': "Type", 'key': 'post_type', 'resize': QHeaderView.Interactive, 'width': 80},
            {'label': "Status", 'key': 'status', 'resize': QHeaderView.Interactive, 'width': 80},
            {'label': "Select", 'key': 'checkbox', 'resize': QHeaderView.Fixed, 'width': 50}
        ]

        if post_type == "Recipes":
            return common_cols_start + [
                {'label': "FB Post", 'key': 'content', 'resize': QHeaderView.Stretch},
                {'label': "Recipe", 'key': 'recipe_text', 'resize': QHeaderView.Stretch},
                {'label': "Website", 'key': 'website_link', 'resize': QHeaderView.Interactive, 'width': 150},
                {'label': "Images", 'key': 'images', 'resize': QHeaderView.Interactive, 'width': 120},
            ] + common_cols_end
        elif post_type == "Engage":
            return common_cols_start + [
                {'label': "Images", 'key': 'images', 'resize': QHeaderView.Interactive, 'width': 120},
                {'label': "Content", 'key': 'content', 'resize': QHeaderView.Stretch},
            ] + common_cols_end
        elif post_type == "Parole":
            return common_cols_start + [
                {'label': "Content", 'key': 'content', 'resize': QHeaderView.Stretch},
            ] + common_cols_end
        else:
            return common_cols_start + common_cols_end # Default fallback

    def select_all_posts(self, checked=True):
        """Select or deselect all posts in this table."""
        for row in range(self.rowCount()):
            widget = self.cellWidget(row, self.columnCount() - 1) # Checkbox is last column
            if widget and isinstance(widget, QWidget): # Check container exists
                checkbox = widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(checked)

    def populate_table(self, posts: list[PostData]):
        """Clears and fills the table with PostData objects."""
        self.setUpdatesEnabled(False) # Disable updates for performance
        self.setSortingEnabled(False) # Disable sorting during population
        self.clearContents()
        self.setRowCount(len(posts))

        status_colors = {
            "posted": QColor("#DFF0D8"), # Light green
            "pending": QColor("#FCF8E3"), # Light yellow
            "failed": QColor("#F2DEDE"), # Light red
            "live": QColor("#DFF0D8"), # Treat live like posted
        }
        default_color = QColor(Qt.white)

        col_map = {col_info['key']: i for i, col_info in enumerate(self.post_columns)}

        for row_idx, post in enumerate(posts):
            # --- Populate Data Cells ---
            for col_key, col_idx in col_map.items():
                if col_key == 'checkbox': continue # Handled separately
                if col_key == 'images': continue # Handled separately

                value = getattr(post, col_key, "") # Get value from PostData object
                if value is None: value = ""

                # Truncate long text for display
                if col_key in ['content', 'recipe_text'] and len(str(value)) > 100:
                    display_value = str(value)[:100] + "..."
                else:
                    display_value = str(value)

                item = QTableWidgetItem(display_value)
                item.setData(Qt.UserRole, post.id) # Store DB ID in item data

                 # Set tooltip for long text
                if display_value != str(value):
                     item.setToolTip(str(value))

                # Alignment (optional)
                if col_key == 'display_pid':
                    item.setTextAlignment(Qt.AlignCenter)
                elif col_key in ['post_type', 'status']:
                     item.setTextAlignment(Qt.AlignCenter)


                self.setItem(row_idx, col_idx, item)

            # --- Image Thumbnails ---
            if 'images' in col_map:
                images_col_idx = col_map['images']
                image_paths = post.image_list
                if image_paths:
                    thumb_widget = QWidget()
                    thumb_layout = QHBoxLayout(thumb_widget)
                    thumb_layout.setContentsMargins(2, 2, 2, 2)
                    thumb_layout.setSpacing(3)
                    thumb_layout.setAlignment(Qt.AlignCenter) # Center thumbnails

                    # Limit number of thumbnails shown for performance
                    max_thumbs = 3
                    for i, img_path in enumerate(image_paths):
                         if i >= max_thumbs:
                              # Add label indicating more images
                              more_label = QLabel(f"+{len(image_paths) - max_thumbs}")
                              more_label.setToolTip(f"{len(image_paths)} total images")
                              thumb_layout.addWidget(more_label)
                              break

                         pixmap = create_thumbnail(img_path, SMALL_THUMBNAIL_SIZE)
                         if not pixmap.isNull():
                            thumb_label = QLabel()
                            thumb_label.setPixmap(pixmap)
                            thumb_label.setToolTip(os.path.basename(img_path))
                            # Optionally make clickable: thumb_label.mousePressEvent = lambda e, p=img_path: self.parent().showPreview(p)
                            thumb_layout.addWidget(thumb_label)

                    if thumb_layout.count() == 0: # If no valid images found
                         thumb_layout.addWidget(QLabel("N/A"))

                    self.setCellWidget(row_idx, images_col_idx, thumb_widget)
                    # Adjust row height slightly if thumbnails are present
                    self.setRowHeight(row_idx, 40)
                else:
                    item = QTableWidgetItem("N/A")
                    item.setTextAlignment(Qt.AlignCenter)
                    self.setItem(row_idx, images_col_idx, item)


            # --- Checkbox ---
            checkbox_col_idx = col_map['checkbox']
            check = QCheckBox()
            # Store post ID in checkbox property for easy retrieval
            check.setProperty("post_id", post.id)

            check_container = QWidget() # Use container for centering
            check_layout = QHBoxLayout(check_container)
            check_layout.addWidget(check)
            check_layout.setAlignment(Qt.AlignCenter)
            check_layout.setContentsMargins(0, 0, 0, 0)
            self.setCellWidget(row_idx, checkbox_col_idx, check_container)

            # --- Row Color Based on Status ---
            row_color = status_colors.get(post.status.lower(), default_color)
            for col_idx in range(self.columnCount()):
                if self.item(row_idx, col_idx):
                    self.item(row_idx, col_idx).setBackground(row_color)
                if self.cellWidget(row_idx, col_idx):
                     # Apply to container widgets too
                     self.cellWidget(row_idx, col_idx).setStyleSheet(f"background-color: {row_color.name()};")


        self.setSortingEnabled(True) # Re-enable sorting
        self.setUpdatesEnabled(True) # Re-enable updates

    def get_selected_post_ids(self) -> list[int]:
        """Returns a list of database IDs for rows where the checkbox is checked."""
        selected_ids = []
        for row in range(self.rowCount()):
            widget = self.cellWidget(row, self.columnCount() - 1) # Checkbox is last column
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    post_id = checkbox.property("post_id")
                    if post_id is not None:
                        selected_ids.append(post_id)
        return selected_ids

# ThumbnailLabel and LargePreviewLabel seem okay, maybe add minor styling/tooltips.
class ThumbnailLabel(QLabel):
    # Add a signal to request showing a larger preview
    imageClicked = pyqtSignal(str)

    def __init__(self, imagePath, size=SMALL_THUMBNAIL_SIZE, parent=None):
        super().__init__(parent)
        self.imagePath = imagePath
        self.setFixedSize(size[0], size[1])
        self.setScaledContents(False) # Keep aspect ratio better
        self.setAlignment(Qt.AlignCenter)

        pixmap = create_thumbnail(imagePath, size)
        if not pixmap.isNull():
            self.setPixmap(pixmap)
            self.setToolTip(f"{os.path.basename(imagePath)}\nClick to view larger")
        else:
            self.setText("ERR") # Indicate loading error
            self.setToolTip(f"Error loading image:\n{imagePath}")

        self.setCursor(Qt.PointingHandCursor)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton and self.imagePath:
            self.imageClicked.emit(self.imagePath) # Emit signal on click
        super().mousePressEvent(event)


class ImagePreviewDialog(QDialog):
    """Simple dialog to show a larger image."""
    def __init__(self, image_path, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"Preview - {os.path.basename(image_path)}")

        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.image_label)

        layout = QVBoxLayout(self)
        layout.addWidget(scroll_area)

        # Load full image
        pixmap = QPixmap(image_path)
        if pixmap.isNull():
            self.image_label.setText(f"Could not load image:\n{image_path}")
            self.resize(300, 200)
        else:
             # Scale down if image is very large to fit screen initially
            screen_geometry = QApplication.primaryScreen().availableGeometry()
            max_width = screen_geometry.width() * 0.8
            max_height = screen_geometry.height() * 0.8
            if pixmap.width() > max_width or pixmap.height() > max_height:
                pixmap = pixmap.scaled(int(max_width), int(max_height), Qt.KeepAspectRatio, Qt.SmoothTransformation)

            self.image_label.setPixmap(pixmap)
             # Resize dialog to fit image, up to screen limits
            self.resize(min(pixmap.width() + 40, int(max_width)+40),
                        min(pixmap.height() + 40, int(max_height)+40) )


# CreatePostDialog & EditPostDialog: Streamline, improve image handling UI.
class PostDialogBase(QDialog):
    """Base class for Create/Edit Post dialogs."""
    postSaved = pyqtSignal(object) # Emit PostData object or dict

    def __init__(self, post_type, parent=None, post_data: PostData | None = None):
        super().__init__(parent)
        self.post_type = post_type
        self.post_data = post_data if post_data else PostData(post_type=post_type) # Work with PostData
        self.is_edit_mode = post_data is not None

        self.setWindowTitle(f"{'Edit' if self.is_edit_mode else 'Create'} Post - {post_type}")
        self.setMinimumWidth(550)
        # Apply consistent styling
        self.setStyleSheet("""
            QDialog { background-color: #FFFFFF; font-size: 11pt; }
            QLabel { margin-bottom: 3px; font-weight: bold; }
            QLineEdit, QTextEdit, QComboBox { padding: 5px; border: 1px solid #CCCCCC; border-radius: 3px; }
            QTextEdit { min-height: 80px; }
            QPushButton { padding: 8px 15px; margin-top: 10px; }
            #imageScrollArea { border: 1px dashed #AAAAAA; background-color: #F9F9F9; }
        """)

        mainLayout = QVBoxLayout(self)
        formLayout = QFormLayout()
        formLayout.setSpacing(10)
        formLayout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow) # Allow fields to expand

        # --- Fields based on Post Type ---
        self.contentEdit = QTextEdit()
        formLayout.addRow(f"{post_type} Content:", self.contentEdit)
        self.contentEdit.setPlainText(self.post_data.content or "")

        if post_type == "Recipes":
            self.recipeEdit = QTextEdit()
            formLayout.addRow("Full Recipe:", self.recipeEdit)
            self.recipeEdit.setPlainText(self.post_data.recipe_text or "")

            self.websiteEdit = QLineEdit()
            self.websiteEdit.setPlaceholderText("https://example.com/recipe")
            formLayout.addRow("Website Link:", self.websiteEdit)
            self.websiteEdit.setText(self.post_data.website_link or "")

        mainLayout.addLayout(formLayout)

        # --- Image Handling ---
        imageGroupBox = QtWidgets.QGroupBox("Images")
        imageLayout = QVBoxLayout(imageGroupBox)

        self.imagePaths = self.post_data.image_list[:] # Work with a copy

        # Use a flow layout for thumbnails
        self.thumbnailsContainer = QWidget()
        self.thumbnailsFlowLayout = FlowLayout(spacing=5) # Use FlowLayout (implementation below)
        self.thumbnailsContainer.setLayout(self.thumbnailsFlowLayout)

        thumbnailsScroll = QScrollArea()
        thumbnailsScroll.setObjectName("imageScrollArea") # For styling
        thumbnailsScroll.setWidgetResizable(True)
        thumbnailsScroll.setWidget(self.thumbnailsContainer)
        thumbnailsScroll.setMinimumHeight(120)
        thumbnailsScroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        imageButtonsLayout = QHBoxLayout()
        self.addImagesBtn = QPushButton(QIcon.fromTheme("list-add"), "Add Images")
        self.addImagesBtn.setToolTip("Add one or more images")
        self.addImagesBtn.clicked.connect(self.add_images)
        imageButtonsLayout.addWidget(self.addImagesBtn)
        imageButtonsLayout.addStretch()

        if post_type == "Engage":
            self.addImagesBtn.setText("Add Image")
            self.addImagesBtn.setIcon(QIcon.fromTheme("list-add"))
            self.addImagesBtn.setToolTip("Add a single image for Engage post")
            # Optionally limit selection to 1 image later in add_images

        imageLayout.addLayout(imageButtonsLayout)
        imageLayout.addWidget(thumbnailsScroll)
        mainLayout.addWidget(imageGroupBox)

        # --- Action Buttons ---
        buttonLayout = QHBoxLayout()
        self.saveButton = QPushButton(QIcon.fromTheme("document-save"), "Save Post")
        self.saveButton.setDefault(True)
        self.saveButton.clicked.connect(self.save_post)

        self.cancelButton = QPushButton(QIcon.fromTheme("dialog-cancel"), "Cancel")
        self.cancelButton.clicked.connect(self.reject)

        buttonLayout.addStretch()
        buttonLayout.addWidget(self.saveButton)
        buttonLayout.addWidget(self.cancelButton)
        mainLayout.addLayout(buttonLayout)

        self.update_thumbnails_ui() # Initial population

    def add_images(self):
        """Opens file dialog to add images."""
        max_images = 1 if self.post_type == "Engage" else 0 # 0 means unlimited for others
        current_count = len(self.imagePaths)

        if max_images > 0 and current_count >= max_images:
             QMessageBox.information(self, "Image Limit", f"Only {max_images} image allowed for {self.post_type} posts.")
             return

        dialog = QFileDialog(self, "Select Images", "", "Images (*.png *.jpg *.jpeg *.bmp *.gif)")
        if max_images == 1:
             dialog.setFileMode(QFileDialog.ExistingFile)
        else:
             dialog.setFileMode(QFileDialog.ExistingFiles)

        if dialog.exec_():
            new_paths = dialog.selectedFiles()
            added_count = 0
            for path in new_paths:
                 if path not in self.imagePaths:
                      if max_images > 0 and len(self.imagePaths) >= max_images:
                           break # Stop if limit reached mid-selection
                      self.imagePaths.append(path)
                      added_count += 1

            if added_count > 0:
                 self.update_thumbnails_ui()
            if max_images > 0 and len(self.imagePaths) > max_images:
                 QMessageBox.warning(self, "Image Limit Exceeded", f"Selection limit is {max_images}. Excess images were ignored.")


    def update_thumbnails_ui(self):
        """Clears and repopulates the thumbnail area."""
        clearLayout(self.thumbnailsFlowLayout) # Clear existing thumbnails

        if not self.imagePaths:
             # Show placeholder text
             placeholder = QLabel("Click 'Add Images' to upload.")
             placeholder.setStyleSheet("font-style: italic; color: grey;")
             placeholder.setAlignment(Qt.AlignCenter)
             self.thumbnailsFlowLayout.addWidget(placeholder)
             return

        for path in self.imagePaths:
            if os.path.exists(path):
                thumb_widget = ImageThumbnailWidget(path) # Use dedicated widget
                thumb_widget.removeRequested.connect(self.remove_image)
                thumb_widget.viewRequested.connect(self.view_image)
                self.thumbnailsFlowLayout.addWidget(thumb_widget)
            else:
                logging.warning(f"Image path in list does not exist: {path}")
                # Optionally remove invalid paths from self.imagePaths here

    def remove_image(self, image_path):
        """Removes an image path and updates the UI."""
        if image_path in self.imagePaths:
            self.imagePaths.remove(image_path)
            self.update_thumbnails_ui()

    def view_image(self, image_path):
         """Shows a larger preview of the image."""
         preview_dialog = ImagePreviewDialog(image_path, self)
         preview_dialog.exec_()


    def save_post(self):
        """Validates input and emits the post data."""
        # Update PostData object
        self.post_data.content = self.contentEdit.toPlainText().strip()

        # --- Validation ---
        if not self.post_data.content:
            QMessageBox.warning(self, "Input Required", f"{self.post_type} content cannot be empty.")
            self.contentEdit.setFocus()
            return

        if self.post_type == "Recipes":
            self.post_data.recipe_text = self.recipeEdit.toPlainText().strip()
            self.post_data.website_link = self.websiteEdit.text().strip()
            if not self.post_data.recipe_text:
                QMessageBox.warning(self, "Input Required", "Full Recipe content cannot be empty.")
                self.recipeEdit.setFocus()
                return
            if not self.post_data.website_link:
                QMessageBox.warning(self, "Input Required", "Website Link cannot be empty.")
                self.websiteEdit.setFocus()
                return
            # Basic URL validation (optional)
            if not self.post_data.website_link.startswith(('http://', 'https://')):
                 QMessageBox.warning(self, "Invalid Link", "Website Link must start with http:// or https://.")
                 self.websiteEdit.setFocus()
                 return

        # Update image path (store as comma-separated string)
        self.post_data.image_path = ",".join(self.imagePaths)

        # Emit the updated post_data object
        self.postSaved.emit(self.post_data)
        self.accept() # Close dialog


# --- Helper Widgets for PostDialog ---

class FlowLayout(QtWidgets.QLayout):
    """A layout that arranges items like text flow."""
    # Standard FlowLayout implementation (find online or keep if you have one)
    # Source: PyQt examples or search "PyQt FlowLayout"
    def __init__(self, parent=None, spacing=-1):
        super().__init__(parent)
        if parent is not None:
            self.setContentsMargins(0, 0, 0, 0)
        self._spacing = spacing
        self._item_list = []

    def __del__(self):
        item = self.takeAt(0)
        while item:
            item = self.takeAt(0)

    def addItem(self, item):
        self._item_list.append(item)

    def count(self):
        return len(self._item_list)

    def itemAt(self, index):
        if 0 <= index < len(self._item_list):
            return self._item_list[index]
        return None

    def takeAt(self, index):
        if 0 <= index < len(self._item_list):
            return self._item_list.pop(index)
        return None

    def expandingDirections(self):
        return Qt.Orientations(Qt.Orientation(0)) # Not expanding

    def hasHeightForWidth(self):
        return True

    def heightForWidth(self, width):
        return self._do_layout(QtCore.QRect(0, 0, width, 0), True)

    def setGeometry(self, rect):
        super().setGeometry(rect)
        self._do_layout(rect, False)

    def sizeHint(self):
        return self.minimumSize()

    def minimumSize(self):
        size = QtCore.QSize()
        for item in self._item_list:
            size = size.expandedTo(item.minimumSize())
        margin, _, _, _ = self.getContentsMargins()
        size += QtCore.QSize(2 * margin, 2 * margin)
        return size

    def spacing(self):
        if self._spacing >= 0:
            return self._spacing
        else:
            return self.style().pixelMetric(QtWidgets.QStyle.PM_LayoutHorizontalSpacing)

    def _do_layout(self, rect, test_only):
        x = rect.x()
        y = rect.y()
        line_height = 0
        spacing = self.spacing()

        for item in self._item_list:
            next_x = x + item.sizeHint().width() + spacing
            if next_x - spacing > rect.right() and line_height > 0:
                x = rect.x()
                y = y + line_height + spacing
                line_height = 0

            if not test_only:
                item.setGeometry(QtCore.QRect(QtCore.QPoint(x, y), item.sizeHint()))

            x = next_x
            line_height = max(line_height, item.sizeHint().height())

        return y + line_height - rect.y()


class ImageThumbnailWidget(QFrame):
    """Widget to display an image thumbnail with remove/view buttons."""
    removeRequested = pyqtSignal(str)
    viewRequested = pyqtSignal(str)

    def __init__(self, image_path, size=DEFAULT_THUMBNAIL_SIZE, parent=None):
        super().__init__(parent)
        self.image_path = image_path
        self.setFixedSize(size[0] + 10, size[1] + 10) # Add padding for buttons/border
        self.setFrameShape(QFrame.Box)
        self.setFrameShadow(QFrame.Sunken)
        # self.setStyleSheet("ImageThumbnailWidget { border: 1px solid #CCCCCC; border-radius: 3px; }")

        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)

        # --- Thumbnail Label ---
        self.thumb_label = QLabel()
        self.thumb_label.setAlignment(Qt.AlignCenter)
        pixmap = create_thumbnail(image_path, size)
        if not pixmap.isNull():
            self.thumb_label.setPixmap(pixmap)
        else:
            self.thumb_label.setText("Error")
        layout.addWidget(self.thumb_label, 1) # Give label more space

        # --- Button Bar ---
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(3)

        view_btn = QPushButton(QIcon.fromTheme("zoom-in"), "")
        view_btn.setToolTip("View larger image")
        view_btn.setFlat(True)
        view_btn.setFixedSize(18, 18)
        view_btn.setIconSize(QSize(16, 16))
        view_btn.clicked.connect(lambda: self.viewRequested.emit(self.image_path))
        button_layout.addWidget(view_btn)

        remove_btn = QPushButton(QIcon.fromTheme("edit-delete"), "")
        remove_btn.setToolTip("Remove this image")
        remove_btn.setFlat(True)
        remove_btn.setFixedSize(18, 18)
        remove_btn.setIconSize(QSize(16, 16))
        remove_btn.clicked.connect(lambda: self.removeRequested.emit(self.image_path))
        button_layout.addWidget(remove_btn)

        layout.addLayout(button_layout)


# EditAccountDialog: Seems okay.
class EditAccountDialog(QDialog):
    # (Keep existing implementation - maybe minor styling)
     def __init__(self, account_data: AccountData, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Edit Account")
        self.account_data = account_data # Store the AccountData object

        # Basic Styling
        self.setStyleSheet("QDialog { background-color: #F5F5F5; } QPushButton { padding: 6px 12px; }")

        layout = QFormLayout(self)
        layout.setSpacing(10)

        self.fbIdLabel = QLabel(self.account_data.fb_id or "N/A")
        layout.addRow("Facebook UID:", self.fbIdLabel)

        self.fullNameEdit = QLineEdit(self.account_data.full_name or "")
        self.fullNameEdit.setPlaceholderText("Enter account's full name")
        layout.addRow("Full Name:", self.fullNameEdit)

        self.statusCombo = QComboBox() # Use ComboBox for defined statuses
        self.statusCombo.addItems(["live", "restricted", "blocked", "unknown"])
        self.statusCombo.setCurrentText(self.account_data.status or "unknown")
        layout.addRow("Status:", self.statusCombo)

        # Buttons
        buttonLayout = QHBoxLayout()
        self.saveBtn = QPushButton(QIcon.fromTheme("document-save"), "Save")
        self.saveBtn.setDefault(True)
        self.saveBtn.setCursor(Qt.PointingHandCursor)
        self.saveBtn.clicked.connect(self.accept)

        self.cancelBtn = QPushButton(QIcon.fromTheme("dialog-cancel"), "Cancel")
        self.cancelBtn.setCursor(Qt.PointingHandCursor)
        self.cancelBtn.clicked.connect(self.reject)

        buttonLayout.addStretch()
        buttonLayout.addWidget(self.saveBtn)
        buttonLayout.addWidget(self.cancelBtn)
        layout.addRow(buttonLayout)

     def getUpdatedData(self) -> tuple[str, str]:
        """Returns the updated full name and status."""
        new_name = self.fullNameEdit.text().strip()
        new_status = self.statusCombo.currentText()
        return new_name, new_status



# --- Main Application Window ---

class FacebookSchedulerMain(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Facebook Auto Poster & Scheduler")
        self.setWindowIcon(QIcon("uploads/logo.png")) # Ensure logo exists
        self.resize(1300, 850) # Slightly larger default size

        # --- Central Data Store (Consider a dedicated DataManager class later) ---
        self.all_posts_data: dict[str, list[PostData]] = {ptype: [] for ptype in POST_CATEGORIES}
        self.all_accounts_data: list[AccountData] = []
        self.all_schedules_data: dict[int, dict] = {} # {tab_index: schedule_info}

        # --- UI Elements ---
        self._create_actions()
        self._create_widgets()
        self._create_layouts()
        self._create_connections()
        self._apply_styles()

        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("Ready", 3000)

        # --- Background Workers & Timers ---
        self.post_check_timer = QTimer(self)
        self.post_check_timer.timeout.connect(self.run_post_check_worker)
        self.post_check_worker_instance = None # To hold the running worker

        # Start timer (e.g., check every 60 seconds)
        self.post_check_timer.start(60 * 1000)
        self.run_post_check_worker() # Run once immediately on start

        # --- Initial Data Load ---
        self.load_accounts_data()
        self.load_posts_data()
        self.load_schedules_data()
        self.load_pages_data()
        self.load_groups_data()

        self.switchPage("Home") # Start on Home page

    def _create_actions(self):
        # Create QAction objects for menus, toolbars if needed later
        pass

    def _create_widgets(self):
        """Create all the main widgets for the UI."""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # Splitter
        self.splitter = QSplitter(Qt.Horizontal)

        # Sidebar (Navigation)
        self.sidebar = QWidget()
        self.sidebar.setMinimumWidth(180)
        self.sidebar.setMaximumWidth(250)

        self.navButtons = {}
        self.navLayout = QVBoxLayout(self.sidebar)
        self.navLayout.setContentsMargins(10, 10, 10, 10)
        self.navLayout.setSpacing(8)

        nav_items = [
            ("Home", "go-home"),
            ("Accounts", "system-users"), # Renamed from Login Facebook
            ("Posts", "document-multiple"), # Renamed from Facebook Posts
            ("Schedules", "view-calendar-month"), # Renamed from Monthly Schedule
            ("Pages", "document-properties"), # Renamed from Pages Manager
            ("Groups", "folder-multiple"), # Renamed from Groups Manager
        ]
        for name, icon_name in nav_items:
            btn = QPushButton(QIcon.fromTheme(icon_name), f" {name}") # Add space for icon
            btn.setCursor(Qt.PointingHandCursor)
            btn.setObjectName("NavButton") # For styling
            btn.setFixedHeight(35)
            btn.setLayoutDirection(Qt.LeftToRight) # Ensure icon is on the left
            btn.setStyleSheet("text-align: left; padding-left: 10px;") # Align text left
            self.navButtons[name] = btn
            self.navLayout.addWidget(btn)

        self.navLayout.addStretch(1) # Push reset button down

        self.resetBtn = QPushButton(QIcon.fromTheme("edit-reset"), " Reset Software")
        self.resetBtn.setCursor(Qt.PointingHandCursor)
        self.resetBtn.setObjectName("ResetButton") # For styling
        self.resetBtn.setFixedHeight(35)
        self.resetBtn.setStyleSheet("text-align: left; padding-left: 10px;")
        self.navLayout.addWidget(self.resetBtn)

        # Main Content Area (Stacked Widget)
        self.pages = QStackedWidget()
        self.homeWidget = self._create_home_page()
        self.accountsWidget = self._create_accounts_page()
        self.postsWidget = self._create_posts_page()
        self.schedulesWidget = self._create_schedules_page()
        self.pagesWidget = self._create_pages_manager_page()
        self.groupsWidget = self._create_groups_manager_page()

        self.pages.addWidget(self.homeWidget)
        self.pages.addWidget(self.accountsWidget)
        self.pages.addWidget(self.postsWidget)
        self.pages.addWidget(self.schedulesWidget)
        self.pages.addWidget(self.pagesWidget)
        self.pages.addWidget(self.groupsWidget)

    def _create_layouts(self):
        """Setup main layout and splitter."""
        mainLayout = QHBoxLayout(self.central_widget)
        mainLayout.setContentsMargins(0, 0, 0, 0) # No margins for main layout

        self.splitter.addWidget(self.sidebar)
        self.splitter.addWidget(self.pages)
        self.splitter.setSizes([200, 1000]) # Initial sizes
        self.splitter.setStretchFactor(1, 1) # Allow content area to expand more

        mainLayout.addWidget(self.splitter)

    def _create_connections(self):
        """Connect signals and slots."""
        # Navigation buttons
        for name, btn in self.navButtons.items():
            # Use partial to pass the name correctly
            btn.clicked.connect(partial(self.switchPage, name))

        # Reset button
        self.resetBtn.clicked.connect(self.resetSoftware)

        # --- Connections within pages (handled in their creation methods) ---


    def _apply_styles(self):
        """Apply QSS styles to the application."""
        # Load from file or define here
        style_sheet = """
            QMainWindow {
                background-color: #F0F4F8; /* Light blue-grey background */
            }
            QWidget#Sidebar { /* Style sidebar specifically */
                background-color: #FFFFFF;
                border-right: 1px solid #D5DDE5;
            }
            QPushButton#NavButton {
                background-color: transparent;
                border: none; /* No border */
                color: #333333;
                padding: 8px 10px;
                border-radius: 4px; /* Slightly rounded corners */
                text-align: left;
                font-size: 11pt;
            }
            QPushButton#NavButton:hover {
                background-color: #E8EFF5; /* Light hover */
                color: #000000;
            }
            QPushButton#NavButton:checked { /* Style for active button */
                background-color: #D2E3F5; /* Blueish background */
                font-weight: bold;
                color: #1A5F9E; /* Darker blue text */
            }

            QPushButton#ResetButton {
                 background-color: transparent;
                 border: none;
                 color: #D9534F; /* Reddish color */
                 padding: 8px 10px;
                 border-radius: 4px;
                 text-align: left;
                 font-size: 11pt;
            }
             QPushButton#ResetButton:hover {
                 background-color: #FBE8E8; /* Light red hover */
             }


            QStackedWidget {
                background-color: #FFFFFF; /* White background for content pages */
            }

            QTabWidget::pane {
                border-top: 1px solid #D5DDE5;
                background: #FFFFFF;
            }
            QTabBar::tab {
                background: #F0F4F8;
                border: 1px solid #D5DDE5;
                border-bottom: none; /* Match pane border */
                padding: 6px 12px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                color: #555555;
            }
            QTabBar::tab:selected {
                background: #FFFFFF; /* Selected tab matches pane */
                color: #1A5F9E;
                font-weight: bold;
            }
             QTabBar::tab:hover {
                 background: #E8EFF5;
             }

            QTableWidget {
                border: 1px solid #D5DDE5;
                gridline-color: #E0E0E0; /* Lighter grid lines */
                alternate-background-color: #F8FAFC; /* Subtle alternating color */
            }
            QHeaderView::section {
                background-color: #E8EFF5; /* Light blue header */
                padding: 4px;
                border: 1px solid #D5DDE5;
                font-weight: bold;
                color: #333;
            }
             QHeaderView::section:horizontal {
                 border-top: none; /* Avoid double border */
             }


            QPushButton {
                background-color: #6495ED; /* Cornflower blue default */
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #4A90E2; /* Darker blue hover */
            }
             QPushButton:pressed {
                 background-color: #3B7ACC;
             }
            QPushButton:disabled {
                 background-color: #B0C4DE; /* Lighter blue disabled */
                 color: #777777;
             }


            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDateEdit, QTimeEdit {
                border: 1px solid #CCCCCC;
                padding: 5px;
                border-radius: 3px;
                background-color: #FFFFFF;
                font-size: 10pt;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus {
                border: 1px solid #6495ED; /* Highlight focus */
            }
            QComboBox::drop-down {
                border: none; /* Clean dropdown arrow */
            }
            QComboBox::down-arrow {
                 /* Use a standard down arrow icon */
                 image: url(:/qt-project.org/styles/commonstyle/images/downarraow-16.png);
                 width: 14px;
                 height: 14px;
             }

            QGroupBox {
                font-weight: bold;
                border: 1px solid #D5DDE5;
                border-radius: 4px;
                margin-top: 10px; /* Space above groupbox */
                padding-top: 15px; /* Space for title */
                background-color: #FDFEFE;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
                left: 10px; /* Position title */
                color: #1A5F9E;
            }

            QStatusBar {
                background-color: #E8EFF5;
                color: #333;
            }
            QStatusBar::item {
                 border: none; /* Remove borders between items */
             }
        """
        self.setStyleSheet(style_sheet)
        self.sidebar.setObjectName("Sidebar") # Assign ID for specific styling

    # --- Page Creation Methods ---

    def _create_home_page(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20) # Add padding

        titleLabel = QLabel("Dashboard")
        titleLabel.setAlignment(Qt.AlignCenter)
        titleLabel.setStyleSheet("font-size: 20pt; font-weight: bold; color: #3F51B5; margin-bottom: 20px;")
        layout.addWidget(titleLabel)

        # Chart View
        self.postChart = QChart()
        self.postChartView = QChartView(self.postChart)
        self.postChartView.setRenderHint(QPainter.Antialiasing)
        self.postChartView.setMinimumHeight(300) # Ensure chart has size

        # Stats Label
        self.statsLabel = QLabel("Loading stats...")
        self.statsLabel.setStyleSheet("font-size: 12pt; margin-top: 20px;")
        self.statsLabel.setAlignment(Qt.AlignCenter)

        layout.addWidget(self.postChartView)
        layout.addWidget(self.statsLabel)
        layout.addStretch()

        return widget

    def _create_accounts_page(self):
        widget = QWidget()
        mainLayout = QVBoxLayout(widget)
        mainLayout.setContentsMargins(15, 15, 15, 15)
        mainLayout.setSpacing(10)

        tabWidget = QTabWidget()





        # NST Browser Manager Tab
        nstManagerTab = QWidget()
        nstManagerLayout = QVBoxLayout(nstManagerTab)

        # Header
        headerLayout = QHBoxLayout()
        headerLabel = QLabel("NST Browser Profiles Manager")
        headerLabel.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3;")
        headerLayout.addWidget(headerLabel)
        headerLayout.addStretch()

        # Refresh button
        self.refreshNSTBtn = QPushButton(QIcon.fromTheme("view-refresh"), "Refresh Profiles")
        self.refreshNSTBtn.clicked.connect(self.refresh_nst_profiles)
        headerLayout.addWidget(self.refreshNSTBtn)

        nstManagerLayout.addLayout(headerLayout)

        # NST Profiles Table
        self.nstProfilesTable = QTableWidget()
        self.nstProfilesTable.setColumnCount(6)
        self.nstProfilesTable.setHorizontalHeaderLabels([
            "Profile ID", "Name", "Status", "Facebook Status", "Actions", "Extract Data"
        ])

        # Configure table
        header = self.nstProfilesTable.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # Profile ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)     # Name
        header.setSectionResizeMode(2, QHeaderView.Fixed)       # Status
        header.setSectionResizeMode(3, QHeaderView.Fixed)       # Facebook Status
        header.setSectionResizeMode(4, QHeaderView.Fixed)       # Actions
        header.setSectionResizeMode(5, QHeaderView.Fixed)       # Extract Data

        header.resizeSection(0, 150)  # Profile ID
        header.resizeSection(2, 80)   # Status
        header.resizeSection(3, 120)  # Facebook Status
        header.resizeSection(4, 120)  # Actions
        header.resizeSection(5, 120)  # Extract Data

        self.nstProfilesTable.setAlternatingRowColors(True)
        self.nstProfilesTable.setSelectionBehavior(QAbstractItemView.SelectRows)

        nstManagerLayout.addWidget(self.nstProfilesTable)

        # Add Profile IDs Section
        addSectionLayout = QVBoxLayout()

        # Section header
        addHeaderLayout = QHBoxLayout()
        addHeaderLabel = QLabel("Add NST Profile IDs")
        addHeaderLabel.setStyleSheet("font-size: 14px; font-weight: bold; color: #4CAF50; margin-top: 10px;")
        addHeaderLayout.addWidget(addHeaderLabel)
        addHeaderLayout.addStretch()
        addSectionLayout.addLayout(addHeaderLayout)

        # Instructions
        addInstructions = QLabel(
            "Enter NST Browser Profile IDs (one per line) to add directly to accounts.\n"
            "No browser verification needed - profiles will be added directly to the list."
        )
        addInstructions.setStyleSheet("color: #666; font-size: 10px; margin-bottom: 10px;")
        addInstructions.setWordWrap(True)
        addSectionLayout.addWidget(addInstructions)

        # Profile IDs input
        inputLayout = QHBoxLayout()

        # Text area for profile IDs
        self.profileIdsEdit = QTextEdit()
        self.profileIdsEdit.setPlaceholderText(
            "Enter NST Browser Profile IDs (one per line):\n\n"
            "profile_001\n"
            "profile_002\n"
            "profile_003\n"
            "# Comment: Special accounts\n"
            "profile_004\n"
            "..."
        )
        self.profileIdsEdit.setMaximumHeight(120)
        inputLayout.addWidget(self.profileIdsEdit)

        # Buttons for adding
        buttonsLayout = QVBoxLayout()

        self.addDirectBtn = QPushButton("Add to List")
        self.addDirectBtn.clicked.connect(self.add_profiles_to_list_direct)
        self.addDirectBtn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        buttonsLayout.addWidget(self.addDirectBtn)

        self.clearInputBtn = QPushButton("Clear")
        self.clearInputBtn.clicked.connect(lambda: self.profileIdsEdit.clear())
        self.clearInputBtn.setStyleSheet("background-color: #9E9E9E; color: white; padding: 6px;")
        buttonsLayout.addWidget(self.clearInputBtn)

        buttonsLayout.addStretch()
        inputLayout.addLayout(buttonsLayout)

        addSectionLayout.addLayout(inputLayout)
        nstManagerLayout.addLayout(addSectionLayout)

        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("margin: 10px 0px;")
        nstManagerLayout.addWidget(separator)

        # Footer buttons
        footerLayout = QHBoxLayout()

        self.checkAllNSTBtn = QPushButton("Check All Status")
        self.checkAllNSTBtn.clicked.connect(self.check_all_nst_status)
        footerLayout.addWidget(self.checkAllNSTBtn)

        self.addSelectedNSTBtn = QPushButton("Add Selected to Accounts")
        self.addSelectedNSTBtn.clicked.connect(self.add_selected_nst_profiles)
        footerLayout.addWidget(self.addSelectedNSTBtn)

        # Add to Manager Accounts (Headless)
        self.addToManagerBtn = QPushButton("Add All to Manager (Headless)")
        self.addToManagerBtn.clicked.connect(self.add_all_to_manager_headless)
        self.addToManagerBtn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 8px;")
        footerLayout.addWidget(self.addToManagerBtn)

        # Add Custom List to Manager
        self.addCustomListBtn = QPushButton("Add Custom List to Manager")
        self.addCustomListBtn.clicked.connect(self.add_custom_list_to_manager)
        self.addCustomListBtn.setStyleSheet("background-color: #9C27B0; color: white; font-weight: bold; padding: 8px;")
        footerLayout.addWidget(self.addCustomListBtn)

        footerLayout.addStretch()

        nstManagerLayout.addLayout(footerLayout)

        # Add Tabs to Widget
        tabWidget.addTab(nstManagerTab, QIcon.fromTheme("computer"), "NST Browser Manager")


        mainLayout.addWidget(tabWidget)
        return widget

    def _create_posts_page(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Header (Optional)
        # headerLabel = QLabel("Manage Posts") ... layout.addWidget(headerLabel)

        self.postsTabs = QTabWidget()
        self.postsTables: dict[str, PostsTableWidget] = {} # Store table widgets

        for post_type in POST_CATEGORIES:
            table = PostsTableWidget(post_type=post_type)
            # Connect signals if needed, e.g., table.editRequest.connect(...)
            self.postsTables[post_type] = table
            # Add icon based on type?
            icon = QIcon()
            if post_type == "Recipes": icon = QIcon.fromTheme("applications-accessories")
            elif post_type == "Engage": icon = QIcon.fromTheme("mail-mark-unread")
            elif post_type == "Parole": icon = QIcon.fromTheme("security-high")
            self.postsTabs.addTab(table, icon, post_type)

        layout.addWidget(self.postsTabs)

        # Footer Buttons
        footerLayout = QHBoxLayout()
        self.createPostBtn = QPushButton(QIcon.fromTheme("list-add"), "Create New Post")
        self.createPostBtn.clicked.connect(self.open_create_post_dialog)
        footerLayout.addWidget(self.createPostBtn)

        # Select All / Deselect All buttons
        self.selectAllPostsBtn = QPushButton("Select All")
        self.selectAllPostsBtn.clicked.connect(self.select_all_current_posts)
        footerLayout.addWidget(self.selectAllPostsBtn)

        self.deselectAllPostsBtn = QPushButton("Deselect All")
        self.deselectAllPostsBtn.clicked.connect(self.deselect_all_current_posts)
        footerLayout.addWidget(self.deselectAllPostsBtn)

        footerLayout.addStretch()

        self.editSelectedPostBtn = QPushButton(QIcon.fromTheme("document-edit"), "Edit Selected")
        self.editSelectedPostBtn.clicked.connect(self.edit_selected_post)
        footerLayout.addWidget(self.editSelectedPostBtn)

        self.removeSelectedPostsBtn = QPushButton(QIcon.fromTheme("edit-delete"), "Remove Selected")
        self.removeSelectedPostsBtn.setStyleSheet("background-color: #D9534F;") # Red remove button
        self.removeSelectedPostsBtn.clicked.connect(self.remove_selected_posts)
        footerLayout.addWidget(self.removeSelectedPostsBtn)

        layout.addLayout(footerLayout)
        return widget

    def _create_schedules_page(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Header (Optional)
        # mLabel = QLabel("Monthly Schedules") ... layout.addWidget(mLabel)

        self.schedulesTabWidget = QTabWidget()
        self.schedulesTabWidget.setTabsClosable(True) # Allow closing tabs
        self.schedulesTabWidget.tabCloseRequested.connect(self.remove_schedule_tab) # Handle close request
        layout.addWidget(self.schedulesTabWidget)

        # Footer Buttons
        mBtnLayout = QHBoxLayout()
        self.createScheduleBtn = QPushButton(QIcon.fromTheme("calendar-new"), "Create New Schedule")
        self.createScheduleBtn.clicked.connect(self.open_create_schedule_dialog)
        mBtnLayout.addWidget(self.createScheduleBtn)

        self.refreshSchedulesBtn = QPushButton(QIcon.fromTheme("view-refresh"), "Refresh Schedules")
        self.refreshSchedulesBtn.setToolTip("Reload post content/images in all schedules from the database")
        self.refreshSchedulesBtn.clicked.connect(self.refresh_all_schedules)
        mBtnLayout.addWidget(self.refreshSchedulesBtn)

        mBtnLayout.addStretch()

        # Buttons for the *current* schedule tab (might need enabling/disabling)
        self.selectAllScheduleBtn = QPushButton("Select All Rows")
        self.selectAllScheduleBtn.clicked.connect(self.select_all_schedule_rows)
        mBtnLayout.addWidget(self.selectAllScheduleBtn)

        self.deselectAllScheduleBtn = QPushButton("Deselect All Rows")
        self.deselectAllScheduleBtn.clicked.connect(self.deselect_all_schedule_rows)
        mBtnLayout.addWidget(self.deselectAllScheduleBtn)

        self.removeSelectedScheduleRowsBtn = QPushButton(QIcon.fromTheme("edit-delete"), "Remove Selected Rows")
        self.removeSelectedScheduleRowsBtn.setStyleSheet("background-color: #D9534F;")
        self.removeSelectedScheduleRowsBtn.clicked.connect(self.remove_selected_schedule_rows)
        mBtnLayout.addWidget(self.removeSelectedScheduleRowsBtn)

        layout.addLayout(mBtnLayout)

        # Initially disable buttons that depend on a selected tab
        self.selectAllScheduleBtn.setEnabled(False)
        self.deselectAllScheduleBtn.setEnabled(False)
        self.removeSelectedScheduleRowsBtn.setEnabled(False)
        self.schedulesTabWidget.currentChanged.connect(self._update_schedule_button_states)


        return widget

    def _create_pages_manager_page(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        header = QLabel("Manage Facebook Pages")
        header.setStyleSheet("font-size: 14pt; font-weight: bold; color: #333; margin-bottom: 10px;")
        layout.addWidget(header)

        self.pagesTable = QTableWidget() # Standard table
        self.pagesTable.setColumnCount(4)
        self.pagesTable.setHorizontalHeaderLabels(["", "Name", "Page ID", "Link"])
        # Setup header resizing
        hHeader = self.pagesTable.horizontalHeader()
        hHeader.setSectionResizeMode(1, QHeaderView.Stretch) # Name stretch
        hHeader.setSectionResizeMode(0, QHeaderView.Fixed) # Checkbox fixed
        hHeader.setSectionResizeMode(2, QHeaderView.Interactive) # ID interactive
        hHeader.setSectionResizeMode(3, QHeaderView.Interactive) # Link interactive
        hHeader.resizeSection(0, 30)
        hHeader.resizeSection(2, 150)
        hHeader.resizeSection(3, 200)
        self.pagesTable.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.pagesTable.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.pagesTable.verticalHeader().setVisible(False)
        self.pagesTable.setAlternatingRowColors(True)
        self.pagesTable.itemDoubleClicked.connect(self.open_page_or_group_link) # Double click to open link
        layout.addWidget(self.pagesTable)

        # Footer Buttons
        footerLayout = QHBoxLayout()
        self.pagesSelectAllBtn = QPushButton("Select All")
        self.pagesSelectAllBtn.clicked.connect(lambda: self._select_all_table_rows(self.pagesTable, True))
        footerLayout.addWidget(self.pagesSelectAllBtn)

        self.pagesDeselectAllBtn = QPushButton("Deselect All")
        self.pagesDeselectAllBtn.clicked.connect(lambda: self._select_all_table_rows(self.pagesTable, False))
        footerLayout.addWidget(self.pagesDeselectAllBtn)

        footerLayout.addStretch()

        self.pagesFetchBtn = QPushButton(QIcon.fromTheme("view-refresh"), "Fetch Pages from Account")
        self.pagesFetchBtn.clicked.connect(lambda: self.fetch_pages_or_groups_for_manager(fetch_pages=True))
        footerLayout.addWidget(self.pagesFetchBtn)

        layout.addLayout(footerLayout)
        return widget

    def _create_groups_manager_page(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        header = QLabel("Manage Facebook Groups")
        header.setStyleSheet("font-size: 14pt; font-weight: bold; color: #333; margin-bottom: 10px;")
        layout.addWidget(header)

        self.groupsTable = QTableWidget() # Standard table
        self.groupsTable.setColumnCount(4)
        self.groupsTable.setHorizontalHeaderLabels(["", "Name", "Group ID", "Link"])
         # Setup header resizing (same as pages)
        hHeader = self.groupsTable.horizontalHeader()
        hHeader.setSectionResizeMode(1, QHeaderView.Stretch)
        hHeader.setSectionResizeMode(0, QHeaderView.Fixed)
        hHeader.setSectionResizeMode(2, QHeaderView.Interactive)
        hHeader.setSectionResizeMode(3, QHeaderView.Interactive)
        hHeader.resizeSection(0, 30)
        hHeader.resizeSection(2, 150)
        hHeader.resizeSection(3, 200)
        self.groupsTable.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.groupsTable.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.groupsTable.verticalHeader().setVisible(False)
        self.groupsTable.setAlternatingRowColors(True)
        self.groupsTable.itemDoubleClicked.connect(self.open_page_or_group_link) # Double click to open link
        layout.addWidget(self.groupsTable)

        # Footer Buttons
        footerLayout = QHBoxLayout()
        self.groupsSelectAllBtn = QPushButton("Select All")
        self.groupsSelectAllBtn.clicked.connect(lambda: self._select_all_table_rows(self.groupsTable, True))
        footerLayout.addWidget(self.groupsSelectAllBtn)

        self.groupsDeselectAllBtn = QPushButton("Deselect All")
        self.groupsDeselectAllBtn.clicked.connect(lambda: self._select_all_table_rows(self.groupsTable, False))
        footerLayout.addWidget(self.groupsDeselectAllBtn)

        footerLayout.addStretch()

        self.groupsFetchBtn = QPushButton(QIcon.fromTheme("view-refresh"), "Fetch Groups from Account")
        self.groupsFetchBtn.clicked.connect(lambda: self.fetch_pages_or_groups_for_manager(fetch_pages=False))
        footerLayout.addWidget(self.groupsFetchBtn)

        layout.addLayout(footerLayout)
        return widget

    # --- Data Loading Methods ---

    def load_accounts_data(self):
        """Loads account data from DB and updates UI."""
        logging.debug("Loading accounts data...")
        try:
            with QMutexLocker(db_mutex):
                rows = c.execute("SELECT id, fb_id, full_name, cookie, status, nst_profile_id, browser_type FROM accounts ORDER BY full_name").fetchall()
            self.all_accounts_data = [AccountData(**row) for row in rows]
            logging.info(f"Loaded {len(self.all_accounts_data)} accounts.")
            # Update UI (Manage Accounts Table) - removed since Manager Accounts tab was deleted
        except sqlite3.Error as e:
            logging.error(f"Failed to load accounts: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not load accounts: {e}")
        self.update_dashboard() # Update dashboard stats

    def load_posts_data(self):
        """Loads post data from DB and updates UI."""
        logging.debug("Loading posts data...")
        try:
            with QMutexLocker(db_mutex):
                rows = c.execute("""
                    SELECT id, content, website_link, image_path, scheduled_time,
                           account_id, status, created_at, post_type, recipe_text
                    FROM posts ORDER BY created_at DESC
                """).fetchall()

            # Clear existing data
            for ptype in self.all_posts_data:
                 self.all_posts_data[ptype] = []

            # Process rows into PostData objects
            for row_dict in rows:
                 post = PostData(**row_dict)
                 # Convert timestamp if stored differently (e.g., TEXT)
                 # if isinstance(post.scheduled_time, str): post.scheduled_time = ...
                 # if isinstance(post.created_at, str): post.created_at = ...
                 if post.post_type in self.all_posts_data:
                     self.all_posts_data[post.post_type].append(post)

            logging.info(f"Loaded {len(rows)} total posts.")

            # Update UI (Post Tables)
            if hasattr(self, 'postsTables'):
                for post_type, table_widget in self.postsTables.items():
                    table_widget.populate_table(self.all_posts_data.get(post_type, []))

                # Update tab titles with post counts
                self.update_posts_tab_titles()

        except sqlite3.Error as e:
            logging.error(f"Failed to load posts: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not load posts: {e}")
        self.update_dashboard() # Update dashboard stats

    def load_schedules_data(self):
        """Loads saved schedules from DB and populates tabs."""
        logging.debug("Loading schedules data...")
        try:
            with QMutexLocker(db_mutex):
                rows = c.execute("SELECT id, schedule_json, created_at FROM schedules ORDER BY created_at").fetchall()

            # Clear existing tabs and internal data first
            self.schedulesTabWidget.clear()
            self.all_schedules_data.clear()

            if not rows:
                self._add_placeholder_schedule_tab()
                return

            # Add tabs for each schedule
            for row in rows:
                schedule_id = row['id']
                try:
                    schedule_list = json.loads(row['schedule_json'])
                    if isinstance(schedule_list, list):
                         self._add_schedule_tab(schedule_list, schedule_id, tab_name=f"Schedule {schedule_id}")
                    else:
                         logging.warning(f"Schedule ID {schedule_id} JSON is not a list, skipping.")
                except json.JSONDecodeError:
                    logging.error(f"Failed to decode JSON for schedule ID {schedule_id}, skipping.")

            logging.info(f"Loaded {len(rows)} schedules.")
            if self.schedulesTabWidget.count() == 0: # Add placeholder if loading failed
                 self._add_placeholder_schedule_tab()

        except sqlite3.Error as e:
            logging.error(f"Failed to load schedules: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not load schedules: {e}")
            self._add_placeholder_schedule_tab() # Add placeholder on error

        self._update_schedule_button_states() # Update buttons based on current tab

    def load_pages_data(self):
        """Load pages data from database."""
        try:
            with QMutexLocker(db_mutex):
                c.execute("SELECT id, name, page_id, url, source FROM pages ORDER BY name")
                rows = c.fetchall()

                self.all_pages_data = []
                for row in rows:
                    page = {
                        'id': row[0],
                        'name': row[1],
                        'page_id': row[2],
                        'url': row[3],
                        'source': row[4]
                    }
                    self.all_pages_data.append(page)

                logging.info(f"Loaded {len(self.all_pages_data)} pages from database")

                # Update UI if exists
                if hasattr(self, 'pagesTable'):
                    self.populate_pages_table()

        except Exception as e:
            logging.error(f"Error loading pages data: {e}")
            self.all_pages_data = []

    def load_groups_data(self):
        """Load groups data from database."""
        try:
            with QMutexLocker(db_mutex):
                c.execute("SELECT id, name, group_id, url, source FROM groups ORDER BY name")
                rows = c.fetchall()

                self.all_groups_data = []
                for row in rows:
                    group = {
                        'id': row[0],
                        'name': row[1],
                        'group_id': row[2],
                        'url': row[3],
                        'source': row[4]
                    }
                    self.all_groups_data.append(group)

                logging.info(f"Loaded {len(self.all_groups_data)} groups from database")

                # Update UI if exists
                if hasattr(self, 'groupsTable'):
                    self.populate_groups_table()

        except Exception as e:
            logging.error(f"Error loading groups data: {e}")
            self.all_groups_data = []

    def _add_placeholder_schedule_tab(self):
        """Adds a tab indicating no schedules are present."""
        placeholder_widget = QWidget()
        layout = QVBoxLayout(placeholder_widget)
        label = QLabel("No schedules created yet.\n\nClick 'Create New Schedule' below to get started.")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("font-size: 12pt; color: grey;")
        layout.addWidget(label)
        self.schedulesTabWidget.addTab(placeholder_widget, QIcon.fromTheme("calendar-view-day"), "No Schedules")
        self.schedulesTabWidget.setTabsClosable(False) # Can't close placeholder


    # --- UI Update and Action Methods ---

    def switchPage(self, sectionName):
        """Switches the main content area to the selected page."""
        logging.debug(f"Switching page to: {sectionName}")
        mapping = {
            "Home": 0, "Accounts": 1, "Posts": 2,
            "Schedules": 3, "Pages": 4, "Groups": 5
        }
        index = mapping.get(sectionName, 0)
        self.pages.setCurrentIndex(index)
        self._updateNavButtonStyles(sectionName)

        # Trigger data refresh/UI update for the selected page if needed
        if sectionName == "Home":
            self.update_dashboard()
        elif sectionName == "Accounts":
            # Account data is loaded initially, maybe refresh status?
            pass
        elif sectionName == "Posts":
            # Post data loaded initially
            pass
        elif sectionName == "Schedules":
             self._update_schedule_button_states()


    def _updateNavButtonStyles(self, selectedName):
        """Updates the visual state of navigation buttons."""
        for name, btn in self.navButtons.items():
            btn.setChecked(name == selectedName) # Use setChecked for QSS :checked state


    def update_dashboard(self):
        """Updates the home page dashboard chart and stats."""
        if not hasattr(self, 'postChart') or not self.pages.currentWidget() == self.homeWidget:
            return # Don't update if chart doesn't exist or Home isn't visible

        logging.debug("Updating dashboard...")

        # --- Update Pie Chart ---
        self.postChart.removeAllSeries() # Clear previous data
        series = QPieSeries()
        series.setHoleSize(0.35) # Make it a donut chart

        post_counts = {}
        total_posts = 0
        try:
             with QMutexLocker(db_mutex):
                  rows = c.execute("SELECT post_type, COUNT(*) as count FROM posts GROUP BY post_type").fetchall()
             for row in rows:
                  ptype = row['post_type'] if row['post_type'] else "Uncategorized"
                  count = row['count']
                  post_counts[ptype] = count
                  total_posts += count
        except sqlite3.Error as e:
             logging.error(f"Dashboard DB error (post counts): {e}")
             self.statsLabel.setText("Error loading post counts.")
             return # Stop update if DB fails

        if total_posts == 0:
            slice_ = series.append("No Posts Yet", 1)
            slice_.setColor(QColor("#E0E0E0")) # Grey color for no posts
            slice_.setLabelVisible(True)
        else:
            # Sort by count descending for better visual hierarchy
            sorted_counts = sorted(post_counts.items(), key=lambda item: item[1], reverse=True)
            for ptype, count in sorted_counts:
                # Add slice with count and percentage
                percentage = (count / total_posts) * 100 if total_posts > 0 else 0
                slice_ = series.append(f"{ptype} ({count})", count)
                slice_.setLabel(f"{ptype}\n{percentage:.1f}%") # Label with percentage
                slice_.setLabelVisible(True)
                # Add hover effect (optional)
                # slice_.hovered.connect(lambda hovered, s=slice_: self._handle_slice_hover(hovered, s))

        self.postChart.addSeries(series)
        self.postChart.setTitle("Posts by Category")
        self.postChart.legend().setVisible(True)
        self.postChart.legend().setAlignment(Qt.AlignRight)
        self.postChart.setAnimationOptions(QChart.SeriesAnimations) # Add animation

        # --- Update Stats Label ---
        stats_lines = [f"<b>Total Posts:</b> {total_posts}"]
        status_counts = {}
        try:
             with QMutexLocker(db_mutex):
                  rows_status = c.execute("SELECT status, COUNT(*) as count FROM posts GROUP BY status").fetchall()
             for row in rows_status:
                  status = row['status'] if row['status'] else "Unknown"
                  status_counts[status.capitalize()] = row['count']

             total_accounts = len(self.all_accounts_data) # Use loaded data
             live_accounts = sum(1 for acc in self.all_accounts_data if acc.status.lower() == 'live')
             stats_lines.append(f"<b>Total Accounts:</b> {total_accounts} ({live_accounts} Live)")

             if status_counts:
                  stats_lines.append("<hr><b>Post Status:</b>")
                  # Sort status for consistent order
                  for status in sorted(status_counts.keys()):
                        stats_lines.append(f"&nbsp;&nbsp;&nbsp;{status}: {status_counts[status]}")

        except sqlite3.Error as e:
             logging.error(f"Dashboard DB error (status counts): {e}")
             stats_lines.append("<font color='red'>Error loading status counts.</font>")

        self.statsLabel.setText("<br>".join(stats_lines))
        logging.debug("Dashboard updated.")


    # --- Account Management Actions ---
    # Traditional Facebook login removed - now using NST Browser profiles only

    # Traditional Facebook login functions removed - now using NST Browser profiles only




    # --- NST Browser Integration Methods ---

# update_account_combo removed - no longer needed since we don't link to existing accounts









    def _add_nst_profile_task(self, profile_id):
        """Add NST profile task - extract Facebook info."""
        try:
            # Kill any existing processes
            kill_nstchrome_processes()
            time.sleep(2)

            # Launch browser
            debugger_address = launch_and_connect_to_browser(profile_id)
            if not debugger_address:
                raise Exception("Failed to launch NST Browser")

            # Connect with Selenium
            test_driver = exec_selenium_nst(debugger_address)
            if not test_driver:
                raise Exception("Failed to connect Selenium to NST Browser")

            # Navigate to Facebook
            test_driver.get("https://www.facebook.com")
            time.sleep(5)

            # Check if logged in
            if "login" in test_driver.current_url.lower():
                raise Exception("Not logged in to Facebook. Please log in to Facebook in this NST Browser profile first.")

            # Extract Facebook info
            real_uid = _extract_fb_uid_selenium(test_driver)
            real_name = _extract_full_name_selenium(test_driver)

            # Fallback if extraction fails
            fb_id = real_uid if real_uid else f"nst_{profile_id}_{random.randint(100,999)}"
            full_name = real_name if real_name else f"NST User {profile_id}"

            # Close browser
            test_driver.quit()

            return {
                "profile_id": profile_id,
                "fb_id": fb_id,
                "full_name": full_name
            }

        except Exception as e:
            logging.error(f"NST profile add failed: {e}")
            raise e

    def on_nst_add_finished(self, result, error):
        """Handle NST profile add result."""
        self.addNSTBtn.setEnabled(True)
        self.addNSTBtn.setText("Add NST Profile")

        if error:
            QMessageBox.critical(self, "Add Profile Failed",
                               f"Failed to add NST Profile.\nError: {error}\n\n"
                               "Please ensure:\n"
                               "1. NST Browser is running\n"
                               "2. Profile ID is correct\n"
                               "3. Facebook is logged in for this profile")
            self.statusBar.showMessage(f"Add profile failed: {error}", 5000)
        elif result:
            # Save to database
            try:
                with QMutexLocker(db_mutex):
                    # First check if this NST profile is already added
                    c.execute("SELECT id, fb_id, full_name FROM accounts WHERE nst_profile_id = ?", (result['profile_id'],))
                    existing_nst = c.fetchone()

                    # Check if this Facebook ID already exists
                    c.execute("SELECT id, nst_profile_id, full_name FROM accounts WHERE fb_id = ?", (result['fb_id'],))
                    existing_fb = c.fetchone()

                    if existing_nst:
                        QMessageBox.warning(self, "Profile Already Exists",
                                          f"NST Profile {result['profile_id']} is already added!\n\n"
                                          f"Existing account: {existing_nst[2]} ({existing_nst[1]})")
                        self.statusBar.showMessage("NST Profile already exists.", 3000)
                        return

                    if existing_fb:
                        # Facebook account exists but with different NST profile
                        reply = QMessageBox.question(self, "Facebook Account Exists",
                                                   f"Facebook account {result['fb_id']} already exists!\n\n"
                                                   f"Current: {existing_fb[2]} (NST: {existing_fb[1] or 'None'})\n"
                                                   f"New: {result['full_name']} (NST: {result['profile_id']})\n\n"
                                                   f"Do you want to update it with the new NST profile?",
                                                   QMessageBox.Yes | QMessageBox.No)
                        if reply != QMessageBox.Yes:
                            self.statusBar.showMessage("Operation cancelled.", 3000)
                            return

                        # Update existing account
                        c.execute("""
                            UPDATE accounts SET
                                full_name=?, nst_profile_id=?, browser_type='nst', status='live'
                            WHERE fb_id=?
                        """, (result['full_name'], result['profile_id'], result['fb_id']))
                        action = "updated"
                    else:
                        # Insert new account
                        c.execute("""
                            INSERT INTO accounts (fb_id, full_name, nst_profile_id, browser_type, status)
                            VALUES (?, ?, ?, 'nst', 'live')
                        """, (result['fb_id'], result['full_name'], result['profile_id']))
                        action = "added"

                    conn.commit()

                logging.info(f"NST Profile {result['profile_id']} {action} successfully: {result['full_name']} ({result['fb_id']})")
                QMessageBox.information(self, f"Profile {action.title()} Successfully",
                                      f"NST Profile {action} successfully!\n\n"
                                      f"Profile ID: {result['profile_id']}\n"
                                      f"Facebook Name: {result['full_name']}\n"
                                      f"Facebook ID: {result['fb_id']}")
                self.statusBar.showMessage(f"NST Profile {result['profile_id']} {action}.", 4000)

                # Clear form and refresh data
                self.nstProfileEdit.clear()
                self.load_accounts_data()

            except sqlite3.Error as e:
                logging.error(f"Database error saving NST profile: {e}")
                QMessageBox.critical(self, "Database Error", f"Could not save NST profile: {e}")
        else:
            QMessageBox.warning(self, "Add Profile Issue", "Profile add completed but with unexpected result.")
            self.statusBar.showMessage("NST profile add completed with unexpected result.", 5000)

    # NST Browser Manager Functions
    def refresh_nst_profiles(self):
        """Refresh NST Browser profiles list."""
        self.refreshNSTBtn.setEnabled(False)
        self.refreshNSTBtn.setText("Refreshing...")
        self.statusBar.showMessage("Loading NST Browser profiles...")

        # Run in background thread
        self.nst_refresh_worker = BaseWorker(self._refresh_nst_profiles_task)
        self.nst_refresh_worker.finished.connect(self.on_nst_refresh_finished)
        self.nst_refresh_worker.start()

    def _refresh_nst_profiles_task(self):
        """Background task to get NST profiles."""
        try:
            profiles = get_nst_profiles_list()
            return {"success": True, "profiles": profiles}
        except Exception as e:
            logging.error(f"Error refreshing NST profiles: {e}")
            return {"success": False, "error": str(e)}

    def on_nst_refresh_finished(self, result, error):
        """Handle NST profiles refresh result."""
        self.refreshNSTBtn.setEnabled(True)
        self.refreshNSTBtn.setText("Refresh Profiles")

        if error or not result or not result.get("success"):
            error_msg = error or result.get("error", "Unknown error")
            QMessageBox.critical(self, "Refresh Failed", f"Failed to refresh NST profiles:\n{error_msg}")
            self.statusBar.showMessage("Refresh failed", 3000)
            return

        profiles = result.get("profiles", [])
        self.populate_nst_profiles_table(profiles)
        self.statusBar.showMessage(f"Found {len(profiles)} NST profiles", 3000)

    def populate_nst_profiles_table(self, profiles):
        """Populate NST profiles table."""
        self.nstProfilesTable.setRowCount(len(profiles))

        for row, profile in enumerate(profiles):
            # Profile ID
            profile_id = profile.get('id', 'Unknown')
            id_item = QTableWidgetItem(profile_id)
            self.nstProfilesTable.setItem(row, 0, id_item)

            # Name
            name = profile.get('name', profile_id)
            name_item = QTableWidgetItem(name)
            self.nstProfilesTable.setItem(row, 1, name_item)

            # Status
            status_item = QTableWidgetItem("Unknown")
            status_item.setTextAlignment(Qt.AlignCenter)
            status_item.setForeground(QColor("#9E9E9E"))
            self.nstProfilesTable.setItem(row, 2, status_item)

            # Facebook Status
            fb_status_item = QTableWidgetItem("Unknown")
            fb_status_item.setTextAlignment(Qt.AlignCenter)
            fb_status_item.setForeground(QColor("#9E9E9E"))
            self.nstProfilesTable.setItem(row, 3, fb_status_item)

            # Actions buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(2, 2, 2, 2)

            check_btn = QPushButton("Check")
            check_btn.setMaximumWidth(60)
            check_btn.clicked.connect(lambda checked, pid=profile_id: self.check_nst_profile_status(pid))
            actions_layout.addWidget(check_btn)

            add_btn = QPushButton("Add")
            add_btn.setMaximumWidth(50)
            add_btn.clicked.connect(lambda checked, pid=profile_id: self.add_single_nst_profile(pid))
            actions_layout.addWidget(add_btn)

            self.nstProfilesTable.setCellWidget(row, 4, actions_widget)

            # Extract Data buttons
            extract_widget = QWidget()
            extract_layout = QHBoxLayout(extract_widget)
            extract_layout.setContentsMargins(2, 2, 2, 2)

            extract_btn = QPushButton("Extract")
            extract_btn.setMaximumWidth(70)
            extract_btn.clicked.connect(lambda checked, pid=profile_id: self.extract_profile_data(pid))
            extract_layout.addWidget(extract_btn)

            self.nstProfilesTable.setCellWidget(row, 5, extract_widget)

    def check_nst_profile_status(self, profile_id):
        """Check status of a specific NST profile."""
        self.statusBar.showMessage(f"Checking status of profile {profile_id}...")

        # Run in background thread
        self.nst_check_worker = BaseWorker(self._check_nst_profile_task, profile_id)
        self.nst_check_worker.finished.connect(lambda result, error: self.on_nst_check_finished(result, error, profile_id))
        self.nst_check_worker.start()

    def _check_nst_profile_task(self, profile_id):
        """Background task to check NST profile status."""
        try:
            return check_nst_profile_status(profile_id)
        except Exception as e:
            logging.error(f"Error checking NST profile {profile_id}: {e}")
            return {"status": "error", "facebook_status": "unknown", "error": str(e)}

    def on_nst_check_finished(self, result, error, profile_id):
        """Handle NST profile check result."""
        if error:
            self.statusBar.showMessage(f"Check failed for {profile_id}", 3000)
            return

        # Update table row
        for row in range(self.nstProfilesTable.rowCount()):
            if self.nstProfilesTable.item(row, 0).text() == profile_id:
                # Update Status
                status = result.get("status", "unknown")
                status_item = self.nstProfilesTable.item(row, 2)
                status_item.setText(status.title())

                if status == "online":
                    status_item.setForeground(QColor("#4CAF50"))
                elif status == "offline":
                    status_item.setForeground(QColor("#F44336"))
                else:
                    status_item.setForeground(QColor("#FF9800"))

                # Update Facebook Status
                fb_status = result.get("facebook_status", "unknown")
                fb_status_item = self.nstProfilesTable.item(row, 3)
                fb_status_item.setText(fb_status.replace("_", " ").title())

                if fb_status == "active":
                    fb_status_item.setForeground(QColor("#4CAF50"))
                elif fb_status == "banned":
                    fb_status_item.setForeground(QColor("#F44336"))
                elif fb_status == "not_logged_in":
                    fb_status_item.setForeground(QColor("#FF9800"))
                elif fb_status == "checkpoint":
                    fb_status_item.setForeground(QColor("#FF5722"))
                else:
                    fb_status_item.setForeground(QColor("#9E9E9E"))

                break

        self.statusBar.showMessage(f"Profile {profile_id}: {status} - Facebook: {fb_status}", 5000)

    def add_single_nst_profile(self, profile_id):
        """Add a single NST profile to accounts."""
        # Set the profile ID in the form and trigger add
        self.nstProfileEdit.setText(profile_id)
        self.add_nst_profile()

    def extract_profile_data(self, profile_id):
        """Extract pages and groups from NST profile."""
        reply = QMessageBox.question(self, "Extract Data",
                                   f"Extract Facebook Pages and Groups from profile {profile_id}?\n\n"
                                   "This will:\n"
                                   "1. Connect to the NST Browser profile\n"
                                   "2. Extract managed pages and groups\n"
                                   "3. Add them to Pages and Groups sections\n\n"
                                   "Make sure Facebook is logged in for this profile.",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        self.statusBar.showMessage(f"Extracting data from profile {profile_id}...")

        # Run in background thread
        self.nst_extract_worker = BaseWorker(self._extract_profile_data_task, profile_id)
        self.nst_extract_worker.finished.connect(lambda result, error: self.on_extract_finished(result, error, profile_id))
        self.nst_extract_worker.start()

    def _extract_profile_data_task(self, profile_id):
        """Background task to extract profile data."""
        try:
            return extract_facebook_pages_and_groups(profile_id)
        except Exception as e:
            logging.error(f"Error extracting data from profile {profile_id}: {e}")
            return {"success": False, "error": str(e), "pages": [], "groups": [], "profile_id": profile_id}

    def on_extract_finished(self, result, error, profile_id):
        """Handle profile data extraction result."""
        if error or not result or not result.get("success"):
            error_msg = error or result.get("error", "Unknown error")
            QMessageBox.critical(self, "Extraction Failed",
                               f"Failed to extract data from profile {profile_id}:\n{error_msg}")
            self.statusBar.showMessage(f"Extraction failed for {profile_id}", 3000)
            return

        pages = result.get("pages", [])
        groups = result.get("groups", [])

        if not pages and not groups:
            QMessageBox.information(self, "No Data Found",
                                  f"No pages or groups found for profile {profile_id}.\n\n"
                                  "This could mean:\n"
                                  "1. The account doesn't manage any pages/groups\n"
                                  "2. Facebook's layout has changed\n"
                                  "3. The account needs to be logged in")
            self.statusBar.showMessage(f"No data found for {profile_id}", 3000)
            return

        # Show results dialog
        self.show_extraction_results(profile_id, pages, groups)

    def show_extraction_results(self, profile_id, pages, groups):
        """Show extraction results and allow user to select what to add."""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"Extracted Data from {profile_id}")
        dialog.setModal(True)
        dialog.resize(600, 500)

        layout = QVBoxLayout(dialog)

        # Header
        header = QLabel(f"Data extracted from NST Profile: {profile_id}")
        header.setStyleSheet("font-size: 14px; font-weight: bold; color: #2196F3; margin-bottom: 10px;")
        layout.addWidget(header)

        # Tabs for pages and groups
        tabs = QTabWidget()

        # Pages tab
        if pages:
            pages_widget = QWidget()
            pages_layout = QVBoxLayout(pages_widget)

            pages_label = QLabel(f"Found {len(pages)} Facebook Pages:")
            pages_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
            pages_layout.addWidget(pages_label)

            pages_table = QTableWidget()
            pages_table.setColumnCount(3)
            pages_table.setHorizontalHeaderLabels(["Select", "Page Name", "URL"])
            pages_table.setRowCount(len(pages))

            for row, page in enumerate(pages):
                # Checkbox
                checkbox = QCheckBox()
                checkbox.setChecked(True)
                pages_table.setCellWidget(row, 0, checkbox)

                # Name
                name_item = QTableWidgetItem(page.get('name', 'Unknown'))
                pages_table.setItem(row, 1, name_item)

                # URL
                url_item = QTableWidgetItem(page.get('url', ''))
                pages_table.setItem(row, 2, url_item)

            pages_table.resizeColumnsToContents()
            pages_layout.addWidget(pages_table)

            tabs.addTab(pages_widget, f"Pages ({len(pages)})")

        # Groups tab
        if groups:
            groups_widget = QWidget()
            groups_layout = QVBoxLayout(groups_widget)

            groups_label = QLabel(f"Found {len(groups)} Facebook Groups:")
            groups_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
            groups_layout.addWidget(groups_label)

            groups_table = QTableWidget()
            groups_table.setColumnCount(3)
            groups_table.setHorizontalHeaderLabels(["Select", "Group Name", "URL"])
            groups_table.setRowCount(len(groups))

            for row, group in enumerate(groups):
                # Checkbox
                checkbox = QCheckBox()
                checkbox.setChecked(True)
                groups_table.setCellWidget(row, 0, checkbox)

                # Name
                name_item = QTableWidgetItem(group.get('name', 'Unknown'))
                groups_table.setItem(row, 1, name_item)

                # URL
                url_item = QTableWidgetItem(group.get('url', ''))
                groups_table.setItem(row, 2, url_item)

            groups_table.resizeColumnsToContents()
            groups_layout.addWidget(groups_table)

            tabs.addTab(groups_widget, f"Groups ({len(groups)})")

        layout.addWidget(tabs)

        # Buttons
        buttons_layout = QHBoxLayout()

        select_all_btn = QPushButton("Select All")
        select_all_btn.clicked.connect(lambda: self._set_all_extraction_checkboxes(tabs, True))
        buttons_layout.addWidget(select_all_btn)

        deselect_all_btn = QPushButton("Deselect All")
        deselect_all_btn.clicked.connect(lambda: self._set_all_extraction_checkboxes(tabs, False))
        buttons_layout.addWidget(deselect_all_btn)

        buttons_layout.addStretch()

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_btn)

        add_btn = QPushButton("Add Selected")
        add_btn.clicked.connect(lambda: self._add_selected_extraction_data(dialog, tabs, pages, groups))
        add_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        buttons_layout.addWidget(add_btn)

        layout.addLayout(buttons_layout)

        dialog.exec_()

    def _set_all_extraction_checkboxes(self, tabs, checked):
        """Set all checkboxes in extraction dialog."""
        for i in range(tabs.count()):
            widget = tabs.widget(i)
            table = widget.findChild(QTableWidget)
            if table:
                for row in range(table.rowCount()):
                    checkbox = table.cellWidget(row, 0)
                    if checkbox:
                        checkbox.setChecked(checked)

    def _add_selected_extraction_data(self, dialog, tabs, pages, groups):
        """Add selected pages and groups to database."""
        selected_pages = []
        selected_groups = []

        # Get selected pages
        if pages:
            pages_widget = None
            for i in range(tabs.count()):
                if "Pages" in tabs.tabText(i):
                    pages_widget = tabs.widget(i)
                    break

            if pages_widget:
                pages_table = pages_widget.findChild(QTableWidget)
                for row in range(pages_table.rowCount()):
                    checkbox = pages_table.cellWidget(row, 0)
                    if checkbox and checkbox.isChecked():
                        selected_pages.append(pages[row])

        # Get selected groups
        if groups:
            groups_widget = None
            for i in range(tabs.count()):
                if "Groups" in tabs.tabText(i):
                    groups_widget = tabs.widget(i)
                    break

            if groups_widget:
                groups_table = groups_widget.findChild(QTableWidget)
                for row in range(groups_table.rowCount()):
                    checkbox = groups_table.cellWidget(row, 0)
                    if checkbox and checkbox.isChecked():
                        selected_groups.append(groups[row])

        if not selected_pages and not selected_groups:
            QMessageBox.warning(dialog, "No Selection", "Please select at least one page or group to add.")
            return

        # Add to database
        try:
            with QMutexLocker(db_mutex):
                added_pages = 0
                added_groups = 0

                # Add pages
                for page in selected_pages:
                    try:
                        c.execute("""
                            INSERT OR IGNORE INTO pages (name, page_id, url, source)
                            VALUES (?, ?, ?, 'NST Browser')
                        """, (page.get('name', 'Unknown'), page.get('page_id', ''), page.get('url', '')))
                        if c.rowcount > 0:
                            added_pages += 1
                    except Exception as e:
                        logging.error(f"Error adding page {page.get('name')}: {e}")

                # Add groups
                for group in selected_groups:
                    try:
                        c.execute("""
                            INSERT OR IGNORE INTO groups (name, group_id, url, source)
                            VALUES (?, ?, ?, 'NST Browser')
                        """, (group.get('name', 'Unknown'), group.get('group_id', ''), group.get('url', '')))
                        if c.rowcount > 0:
                            added_groups += 1
                    except Exception as e:
                        logging.error(f"Error adding group {group.get('name')}: {e}")

                conn.commit()

            # Show success message
            message = f"Successfully added:\n"
            if added_pages > 0:
                message += f"• {added_pages} Facebook Pages\n"
            if added_groups > 0:
                message += f"• {added_groups} Facebook Groups\n"

            if added_pages == 0 and added_groups == 0:
                message = "All selected items were already in the database."

            QMessageBox.information(dialog, "Data Added", message)

            # Refresh data
            self.load_pages_data()
            self.load_groups_data()

            dialog.accept()

        except Exception as e:
            logging.error(f"Error adding extraction data: {e}")
            QMessageBox.critical(dialog, "Database Error", f"Error adding data to database:\n{e}")

    def check_all_nst_status(self):
        """Check status of all NST profiles."""
        if self.nstProfilesTable.rowCount() == 0:
            QMessageBox.information(self, "No Profiles", "No NST profiles loaded. Click 'Refresh Profiles' first.")
            return

        reply = QMessageBox.question(self, "Check All Status",
                                   f"Check status of all {self.nstProfilesTable.rowCount()} NST profiles?\n\n"
                                   "This may take some time...",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        # Collect all profile IDs
        profile_ids = []
        for row in range(self.nstProfilesTable.rowCount()):
            profile_id = self.nstProfilesTable.item(row, 0).text()
            profile_ids.append(profile_id)

        self.statusBar.showMessage(f"Checking status of {len(profile_ids)} profiles...")

        # Run in background thread
        self.nst_check_all_worker = BaseWorker(self._check_all_nst_profiles_task, profile_ids)
        self.nst_check_all_worker.finished.connect(self.on_check_all_finished)
        self.nst_check_all_worker.start()

    def _check_all_nst_profiles_task(self, profile_ids):
        """Background task to check all NST profiles."""
        results = {}
        for profile_id in profile_ids:
            try:
                result = check_nst_profile_status(profile_id)
                results[profile_id] = result
                logging.info(f"Checked {profile_id}: {result.get('status')} - {result.get('facebook_status')}")
            except Exception as e:
                logging.error(f"Error checking {profile_id}: {e}")
                results[profile_id] = {"status": "error", "facebook_status": "unknown", "error": str(e)}

        return results

    def on_check_all_finished(self, results, error):
        """Handle check all profiles result."""
        if error:
            QMessageBox.critical(self, "Check Failed", f"Error checking profiles:\n{error}")
            self.statusBar.showMessage("Check all failed", 3000)
            return

        # Update all rows
        for profile_id, result in results.items():
            for row in range(self.nstProfilesTable.rowCount()):
                if self.nstProfilesTable.item(row, 0).text() == profile_id:
                    # Update Status
                    status = result.get("status", "unknown")
                    status_item = self.nstProfilesTable.item(row, 2)
                    status_item.setText(status.title())

                    if status == "online":
                        status_item.setForeground(QColor("#4CAF50"))
                    elif status == "offline":
                        status_item.setForeground(QColor("#F44336"))
                    else:
                        status_item.setForeground(QColor("#FF9800"))

                    # Update Facebook Status
                    fb_status = result.get("facebook_status", "unknown")
                    fb_status_item = self.nstProfilesTable.item(row, 3)
                    fb_status_item.setText(fb_status.replace("_", " ").title())

                    if fb_status == "active":
                        fb_status_item.setForeground(QColor("#4CAF50"))
                    elif fb_status == "banned":
                        fb_status_item.setForeground(QColor("#F44336"))
                    elif fb_status == "not_logged_in":
                        fb_status_item.setForeground(QColor("#FF9800"))
                    elif fb_status == "checkpoint":
                        fb_status_item.setForeground(QColor("#FF5722"))
                    else:
                        fb_status_item.setForeground(QColor("#9E9E9E"))

                    break

        # Show summary
        online_count = sum(1 for r in results.values() if r.get("status") == "online")
        active_count = sum(1 for r in results.values() if r.get("facebook_status") == "active")

        QMessageBox.information(self, "Check Complete",
                              f"Status check completed!\n\n"
                              f"Online profiles: {online_count}/{len(results)}\n"
                              f"Active Facebook accounts: {active_count}/{len(results)}")

        self.statusBar.showMessage(f"Check complete: {online_count} online, {active_count} active", 5000)

    def add_selected_nst_profiles(self):
        """Add selected NST profiles to accounts."""
        # This would require checkboxes in the table - for now, show info
        QMessageBox.information(self, "Add Selected",
                              "To add profiles to accounts:\n\n"
                              "1. Use the 'Add' button for individual profiles\n"
                              "2. Or use the 'Add NST Profile' tab\n\n"
                              "This ensures proper Facebook data extraction.")

    def add_all_to_manager_headless(self):
        """Add all NST profiles to Manager Accounts using headless extraction."""
        if self.nstProfilesTable.rowCount() == 0:
            QMessageBox.information(self, "No Profiles", "No NST profiles loaded. Click 'Refresh Profiles' first.")
            return

        # Collect all profile IDs
        profile_ids = []
        for row in range(self.nstProfilesTable.rowCount()):
            profile_id = self.nstProfilesTable.item(row, 0).text()
            profile_ids.append(profile_id)

        # Show confirmation dialog
        reply = QMessageBox.question(self, "Add to Manager Accounts",
                                   f"Add all {len(profile_ids)} NST profiles to Manager Accounts?\n\n"
                                   "This will:\n"
                                   "1. Extract Facebook info from each profile (headless)\n"
                                   "2. Add them directly to Manager Accounts\n"
                                   "3. Skip profiles that are already added\n\n"
                                   "This process may take some time...",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        # Start headless processing
        self.start_headless_manager_add(profile_ids)

    def start_headless_manager_add(self, profile_ids):
        """Start headless processing to add profiles to Manager Accounts."""
        self.addToManagerBtn.setEnabled(False)
        self.addToManagerBtn.setText(f"Processing {len(profile_ids)} Profiles...")

        self.statusBar.showMessage(f"Adding {len(profile_ids)} profiles to Manager Accounts (headless)...")

        # Run headless processing in background thread
        self.nst_manager_worker = BaseWorker(self._add_to_manager_headless_task, profile_ids)
        self.nst_manager_worker.finished.connect(self.on_manager_headless_finished)
        self.nst_manager_worker.start()

    def _add_to_manager_headless_task(self, profile_ids):
        """Background task to add profiles to Manager Accounts headless."""
        results = {
            'successful': [],
            'failed': [],
            'skipped': [],
            'total': len(profile_ids)
        }

        for i, profile_id in enumerate(profile_ids):
            try:
                logging.info(f"Processing profile {i+1}/{len(profile_ids)}: {profile_id}")

                # Check if profile already exists in accounts
                with QMutexLocker(db_mutex):
                    c.execute("SELECT id, full_name FROM accounts WHERE nst_profile_id = ?", (profile_id,))
                    existing = c.fetchone()
                    if existing:
                        results['skipped'].append({
                            'profile_id': profile_id,
                            'reason': f'Already exists as: {existing[1]}'
                        })
                        continue

                # Kill any existing processes
                kill_nstchrome_processes()
                time.sleep(2)

                # Launch browser in headless mode
                debugger_address = launch_and_connect_to_browser(profile_id)
                if not debugger_address:
                    raise Exception("Failed to launch NST Browser")

                # Connect with Selenium (headless)
                test_driver = exec_selenium_nst_headless(debugger_address)
                if not test_driver:
                    raise Exception("Failed to connect Selenium to NST Browser")

                try:
                    # Navigate to Facebook
                    test_driver.get("https://www.facebook.com")
                    time.sleep(5)

                    # Check if logged in
                    if "login" in test_driver.current_url.lower():
                        raise Exception("Not logged in to Facebook")

                    # Extract Facebook info
                    real_uid = _extract_fb_uid_selenium(test_driver)
                    real_name = _extract_full_name_selenium(test_driver)

                    # Fallback if extraction fails
                    fb_id = real_uid if real_uid else f"nst_{profile_id}_{random.randint(100,999)}"
                    full_name = real_name if real_name else f"NST User {profile_id}"

                    # Add to database
                    with QMutexLocker(db_mutex):
                        # Double-check for duplicates
                        c.execute("SELECT id FROM accounts WHERE fb_id = ?", (fb_id,))
                        if c.fetchone():
                            results['skipped'].append({
                                'profile_id': profile_id,
                                'reason': f'Facebook ID {fb_id} already exists'
                            })
                            continue

                        # Insert new account
                        c.execute("""
                            INSERT INTO accounts (fb_id, full_name, nst_profile_id, browser_type, status)
                            VALUES (?, ?, ?, 'nst', 'live')
                        """, (fb_id, full_name, profile_id))
                        conn.commit()

                    results['successful'].append({
                        'profile_id': profile_id,
                        'fb_id': fb_id,
                        'full_name': full_name
                    })

                    logging.info(f"Successfully added {profile_id}: {full_name} ({fb_id})")

                finally:
                    # Close browser
                    try:
                        test_driver.quit()
                    except:
                        pass

            except Exception as e:
                error_msg = str(e)
                results['failed'].append({
                    'profile_id': profile_id,
                    'error': error_msg
                })
                logging.error(f"Failed to process {profile_id}: {error_msg}")

                # Try to close driver if it exists
                try:
                    if 'test_driver' in locals():
                        test_driver.quit()
                except:
                    pass

        return results

    def on_manager_headless_finished(self, results, error):
        """Handle headless manager add result."""
        self.addToManagerBtn.setEnabled(True)
        self.addToManagerBtn.setText("Add All to Manager (Headless)")

        if error:
            QMessageBox.critical(self, "Headless Add Failed",
                               f"Failed to add profiles to Manager Accounts.\nError: {error}")
            self.statusBar.showMessage(f"Headless add failed: {error}", 5000)
            return

        if not results:
            QMessageBox.warning(self, "No Results", "No results returned from headless add task.")
            return

        successful = results.get('successful', [])
        failed = results.get('failed', [])
        skipped = results.get('skipped', [])
        total = results.get('total', 0)

        # Show results
        self.show_manager_headless_results(successful, failed, skipped, total)

        # Refresh accounts data
        self.load_accounts_data()

    def show_manager_headless_results(self, successful, failed, skipped, total):
        """Show headless manager add results."""
        # Create results dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("Manager Accounts - Headless Add Results")
        dialog.setModal(True)
        dialog.resize(700, 500)

        layout = QVBoxLayout(dialog)

        # Header
        header = QLabel("Headless Processing Complete!")
        header.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3; margin-bottom: 10px;")
        layout.addWidget(header)

        # Summary
        summary_text = f"📊 Summary:\n"
        summary_text += f"• Total processed: {total}\n"
        summary_text += f"• Successfully added: {len(successful)}\n"
        summary_text += f"• Skipped (already exist): {len(skipped)}\n"
        summary_text += f"• Failed: {len(failed)}\n"

        summary_label = QLabel(summary_text)
        summary_label.setStyleSheet("font-size: 12px; background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(summary_label)

        # Tabs for detailed results
        tabs = QTabWidget()

        # Successful tab
        if successful:
            success_widget = QWidget()
            success_layout = QVBoxLayout(success_widget)

            success_table = QTableWidget()
            success_table.setColumnCount(3)
            success_table.setHorizontalHeaderLabels(["Profile ID", "Facebook Name", "Facebook ID"])
            success_table.setRowCount(len(successful))

            for row, item in enumerate(successful):
                success_table.setItem(row, 0, QTableWidgetItem(item['profile_id']))
                success_table.setItem(row, 1, QTableWidgetItem(item['full_name']))
                success_table.setItem(row, 2, QTableWidgetItem(item['fb_id']))

            success_table.resizeColumnsToContents()
            success_layout.addWidget(success_table)

            tabs.addTab(success_widget, f"✅ Successful ({len(successful)})")

        # Skipped tab
        if skipped:
            skipped_widget = QWidget()
            skipped_layout = QVBoxLayout(skipped_widget)

            skipped_table = QTableWidget()
            skipped_table.setColumnCount(2)
            skipped_table.setHorizontalHeaderLabels(["Profile ID", "Reason"])
            skipped_table.setRowCount(len(skipped))

            for row, item in enumerate(skipped):
                skipped_table.setItem(row, 0, QTableWidgetItem(item['profile_id']))
                skipped_table.setItem(row, 1, QTableWidgetItem(item['reason']))

            skipped_table.resizeColumnsToContents()
            skipped_layout.addWidget(skipped_table)

            tabs.addTab(skipped_widget, f"⚠️ Skipped ({len(skipped)})")

        # Failed tab
        if failed:
            failed_widget = QWidget()
            failed_layout = QVBoxLayout(failed_widget)

            failed_table = QTableWidget()
            failed_table.setColumnCount(2)
            failed_table.setHorizontalHeaderLabels(["Profile ID", "Error"])
            failed_table.setRowCount(len(failed))

            for row, item in enumerate(failed):
                failed_table.setItem(row, 0, QTableWidgetItem(item['profile_id']))
                failed_table.setItem(row, 1, QTableWidgetItem(item['error']))

            failed_table.resizeColumnsToContents()
            failed_layout.addWidget(failed_table)

            tabs.addTab(failed_widget, f"❌ Failed ({len(failed)})")

        layout.addWidget(tabs)

        # Close button
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(dialog.accept)
        close_btn.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 8px;")
        layout.addWidget(close_btn)

        # Show summary in status bar
        self.statusBar.showMessage(f"Headless add complete: {len(successful)} added, {len(skipped)} skipped, {len(failed)} failed", 8000)

        dialog.exec_()

    def add_custom_list_to_manager(self):
        """Add custom list of NST profile IDs to Manager Accounts."""
        # Create input dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("Add Custom Profile List to Manager")
        dialog.setModal(True)
        dialog.resize(500, 400)

        layout = QVBoxLayout(dialog)

        # Header
        header = QLabel("Add Custom NST Profile List to Manager Accounts")
        header.setStyleSheet("font-size: 14px; font-weight: bold; color: #9C27B0; margin-bottom: 10px;")
        layout.addWidget(header)

        # Instructions
        instructions = QLabel(
            "Enter NST Browser Profile IDs (one per line) to add directly to Manager Accounts.\n"
            "This will extract Facebook info headless and add them to accounts.\n"
            "You can add comments with # symbol."
        )
        instructions.setStyleSheet("color: #666; margin-bottom: 10px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Profile IDs input
        profile_label = QLabel("NST Profile IDs:")
        profile_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(profile_label)

        profile_edit = QTextEdit()
        profile_edit.setPlaceholderText(
            "Enter NST Browser Profile IDs (one per line):\n\n"
            "profile_001\n"
            "profile_002\n"
            "# Comment: Special accounts\n"
            "profile_003\n"
            "profile_004\n"
            "..."
        )
        profile_edit.setMaximumHeight(200)
        layout.addWidget(profile_edit)

        # Buttons
        buttons_layout = QHBoxLayout()

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_btn)

        add_btn = QPushButton("Add to Manager (Headless)")
        add_btn.clicked.connect(lambda: self._process_custom_list(dialog, profile_edit.toPlainText()))
        add_btn.setStyleSheet("background-color: #9C27B0; color: white; font-weight: bold; padding: 8px;")
        buttons_layout.addWidget(add_btn)

        layout.addLayout(buttons_layout)

        dialog.exec_()

    def _process_custom_list(self, dialog, profile_text):
        """Process custom profile list."""
        profile_text = profile_text.strip()
        if not profile_text:
            QMessageBox.warning(dialog, "No Input", "Please enter at least one NST Profile ID.")
            return

        # Parse profile IDs
        profile_ids = []
        for line in profile_text.split('\n'):
            profile_id = line.strip()
            if profile_id and not profile_id.startswith('#'):  # Allow comments with #
                profile_ids.append(profile_id)

        if not profile_ids:
            QMessageBox.warning(dialog, "No Valid IDs", "No valid Profile IDs found.")
            return

        # Close dialog
        dialog.accept()

        # Show confirmation
        reply = QMessageBox.question(self, "Add Custom List to Manager",
                                   f"Add {len(profile_ids)} NST profiles to Manager Accounts?\n\n"
                                   "Profile IDs:\n" +
                                   "\n".join([f"• {pid}" for pid in profile_ids[:10]]) +
                                   (f"\n• ... and {len(profile_ids) - 10} more" if len(profile_ids) > 10 else "") +
                                   "\n\nThis will extract Facebook info headless and add them to accounts.",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        # Start processing
        self.start_headless_manager_add(profile_ids)

    def add_profiles_to_list_direct(self):
        """Add profile IDs directly to NST Browser Manager list."""
        profile_text = self.profileIdsEdit.toPlainText().strip()
        if not profile_text:
            QMessageBox.warning(self, "No Input", "Please enter at least one NST Profile ID.")
            return

        # Parse profile IDs
        profile_ids = []
        for line in profile_text.split('\n'):
            profile_id = line.strip()
            if profile_id and not profile_id.startswith('#'):  # Allow comments with #
                profile_ids.append(profile_id)

        if not profile_ids:
            QMessageBox.warning(self, "No Valid IDs", "No valid Profile IDs found.")
            return

        # Check for duplicates in current table
        existing_profiles = []
        for row in range(self.nstProfilesTable.rowCount()):
            existing_id = self.nstProfilesTable.item(row, 0).text()
            if existing_id in profile_ids:
                existing_profiles.append(existing_id)

        # Remove duplicates
        new_profiles = [pid for pid in profile_ids if pid not in existing_profiles]

        if not new_profiles and existing_profiles:
            QMessageBox.information(self, "All Exist",
                                  f"All {len(profile_ids)} Profile IDs are already in the list.")
            return

        # Show confirmation
        message = f"Add {len(profile_ids)} Profile IDs to the list?\n\n"
        if new_profiles:
            message += f"✅ New profiles: {len(new_profiles)}\n"
            for pid in new_profiles[:5]:
                message += f"   • {pid}\n"
            if len(new_profiles) > 5:
                message += f"   • ... and {len(new_profiles) - 5} more\n"

        if existing_profiles:
            message += f"\n⚠️ Already in list: {len(existing_profiles)}\n"
            for pid in existing_profiles[:3]:
                message += f"   • {pid}\n"
            if len(existing_profiles) > 3:
                message += f"   • ... and {len(existing_profiles) - 3} more\n"

        reply = QMessageBox.question(self, "Add to List", message,
                                   QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        # Add new profiles to table
        current_row_count = self.nstProfilesTable.rowCount()
        self.nstProfilesTable.setRowCount(current_row_count + len(new_profiles))

        for i, profile_id in enumerate(new_profiles):
            row = current_row_count + i

            # Profile ID
            id_item = QTableWidgetItem(profile_id)
            self.nstProfilesTable.setItem(row, 0, id_item)

            # Name (same as ID for now)
            name_item = QTableWidgetItem(profile_id)
            self.nstProfilesTable.setItem(row, 1, name_item)

            # Status
            status_item = QTableWidgetItem("Added")
            status_item.setTextAlignment(Qt.AlignCenter)
            status_item.setForeground(QColor("#4CAF50"))
            self.nstProfilesTable.setItem(row, 2, status_item)

            # Facebook Status
            fb_status_item = QTableWidgetItem("Unknown")
            fb_status_item.setTextAlignment(Qt.AlignCenter)
            fb_status_item.setForeground(QColor("#9E9E9E"))
            self.nstProfilesTable.setItem(row, 3, fb_status_item)

            # Actions buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(2, 2, 2, 2)

            check_btn = QPushButton("Check")
            check_btn.setMaximumWidth(60)
            check_btn.clicked.connect(lambda checked, pid=profile_id: self.check_nst_profile_status(pid))
            actions_layout.addWidget(check_btn)

            add_btn = QPushButton("Add")
            add_btn.setMaximumWidth(50)
            add_btn.clicked.connect(lambda checked, pid=profile_id: self.add_single_nst_profile_direct(pid))
            actions_layout.addWidget(add_btn)

            self.nstProfilesTable.setCellWidget(row, 4, actions_widget)

            # Extract Data buttons
            extract_widget = QWidget()
            extract_layout = QHBoxLayout(extract_widget)
            extract_layout.setContentsMargins(2, 2, 2, 2)

            extract_btn = QPushButton("Extract")
            extract_btn.setMaximumWidth(70)
            extract_btn.clicked.connect(lambda checked, pid=profile_id: self.extract_profile_data(pid))
            extract_layout.addWidget(extract_btn)

            self.nstProfilesTable.setCellWidget(row, 5, extract_widget)

        # Clear input and show success
        self.profileIdsEdit.clear()

        QMessageBox.information(self, "Added Successfully",
                              f"Successfully added {len(new_profiles)} Profile IDs to the list!\n\n"
                              f"You can now:\n"
                              f"• Check their status\n"
                              f"• Add them to accounts\n"
                              f"• Extract their data")

        self.statusBar.showMessage(f"Added {len(new_profiles)} Profile IDs to the list", 5000)

    def add_single_nst_profile_direct(self, profile_id):
        """Add a single NST profile directly to accounts without browser verification."""
        # Check if profile already exists in accounts
        existing_account = next((acc for acc in self.all_accounts_data if acc.nst_profile_id == profile_id), None)
        if existing_account:
            QMessageBox.warning(self, "Profile Exists",
                              f"NST Profile '{profile_id}' is already added as account '{existing_account.full_name}'.")
            return

        # Add directly to database without browser verification
        try:
            with QMutexLocker(db_mutex):
                # Generate fallback data
                fb_id = f"nst_{profile_id}_{random.randint(100000, 999999)}"
                full_name = f"NST User {profile_id}"

                # Insert new account
                c.execute("""
                    INSERT INTO accounts (fb_id, full_name, nst_profile_id, browser_type, status)
                    VALUES (?, ?, ?, 'nst', 'pending')
                """, (fb_id, full_name, profile_id))
                conn.commit()

            QMessageBox.information(self, "Profile Added",
                                  f"NST Profile '{profile_id}' added successfully!\n\n"
                                  f"Name: {full_name}\n"
                                  f"ID: {fb_id}\n"
                                  f"Status: Pending (can be updated later)")

            # Refresh accounts data
            self.load_accounts_data()
            self.statusBar.showMessage(f"Profile {profile_id} added to accounts", 3000)

        except Exception as e:
            logging.error(f"Error adding profile {profile_id}: {e}")
            QMessageBox.critical(self, "Database Error", f"Error adding profile to database:\n{e}")

    # End of NST Browser Manager Functions



    def post_to_facebook_with_account(self, account: AccountData, post_content: str, target_page_or_group: str = None, images: list = None):
        """Post to Facebook using NST Browser for the account."""
        driver = None
        try:
            # Create NST Browser driver
            driver = create_driver_for_account(account)

            # Navigate to Facebook (already logged in via NST Browser)
            driver.get("https://www.facebook.com")
            time.sleep(3)

            # Check if we're logged in
            if "login" in driver.current_url.lower():
                raise Exception("Not logged in to Facebook")

            # Navigate to target page/group if specified
            if target_page_or_group:
                # This would need to be implemented based on the target type
                # For now, just post to timeline
                pass

            # Find the post composer
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # Wait for and click the post composer
            composer = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[@role='button' and contains(@aria-label, 'What')]"))
            )
            composer.click()
            time.sleep(2)

            # Find the text area and enter content
            text_area = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//div[@role='textbox']"))
            )
            text_area.send_keys(post_content)
            time.sleep(1)

            # Handle images if provided
            if images:
                # Find and click photo/video button
                photo_btn = driver.find_element(By.XPATH, "//div[@aria-label='Photo/video']")
                photo_btn.click()
                time.sleep(2)

                # Upload images
                file_input = driver.find_element(By.XPATH, "//input[@type='file']")
                for image_path in images:
                    if os.path.exists(image_path):
                        file_input.send_keys(image_path)
                        time.sleep(1)

            # Find and click the post button
            post_btn = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[@role='button' and @aria-label='Post']"))
            )
            post_btn.click()

            # Wait for post to be published
            time.sleep(5)

            logging.info(f"Successfully posted to Facebook using NST Browser for account {account.full_name}")
            return True

        except Exception as e:
            logging.error(f"Failed to post to Facebook for account {account.full_name}: {e}")
            raise e
        finally:
            if driver:
                try:
                    # Leave NST Browser open for future use
                    pass
                except Exception as e:
                    logging.error(f"Error with driver: {e}")



    # --- Post Management Actions ---

    def get_current_posts_table(self) -> PostsTableWidget | None:
         """Gets the currently visible table widget in the Posts tab."""
         if hasattr(self, 'postsTabs'):
              current_widget = self.postsTabs.currentWidget()
              if isinstance(current_widget, PostsTableWidget):
                   return current_widget
         return None

    def select_all_current_posts(self):
        """Select all posts in the currently visible table."""
        current_table = self.get_current_posts_table()
        if current_table:
            current_table.select_all_posts(True)

    def deselect_all_current_posts(self):
        """Deselect all posts in the currently visible table."""
        current_table = self.get_current_posts_table()
        if current_table:
            current_table.select_all_posts(False)

    def update_posts_tab_titles(self):
        """Update tab titles to include post counts."""
        if not hasattr(self, 'postsTabs') or not hasattr(self, 'all_posts_data'):
            return

        for i in range(self.postsTabs.count()):
            tab_widget = self.postsTabs.widget(i)
            if isinstance(tab_widget, PostsTableWidget):
                post_type = tab_widget.post_type
                post_count = len(self.all_posts_data.get(post_type, []))

                # Get the icon for this tab
                icon = self.postsTabs.tabIcon(i)

                # Update tab text with count
                new_title = f"{post_type} ({post_count})"
                self.postsTabs.setTabText(i, new_title)

    def open_create_post_dialog(self):
        """Opens the dialog to create a new post of the current tab's type."""
        current_table = self.get_current_posts_table()
        if not current_table:
            logging.warning("Create Post clicked but no current table found.")
            return

        post_type = current_table.post_type
        dlg = PostDialogBase(post_type=post_type, parent=self)
        # Connect the signal AFTER creating the dialog
        dlg.postSaved.connect(self.save_new_post_data)
        dlg.exec_() # Show dialog modally

    def save_new_post_data(self, post_data: PostData):
        """Receives data from Create dialog and saves to DB."""
        try:
            with QMutexLocker(db_mutex):
                cursor = c.execute('''
                    INSERT INTO posts (content, recipe_text, website_link, image_path, post_type, status, scheduled_time, account_id)
                    VALUES (?, ?, ?, ?, ?, 'pending', ?, ?)
                ''', (
                    post_data.content,
                    post_data.recipe_text,
                    post_data.website_link,
                    post_data.image_path,
                    post_data.post_type,
                    None, # scheduled_time - set later
                    None  # account_id - set later or default
                    ))
                conn.commit()
                new_post_id = cursor.lastrowid # Get the ID of the inserted post
            logging.info(f"New post created successfully (ID: {new_post_id}, Type: {post_data.post_type})")
            QMessageBox.information(self, "Success", f"{post_data.post_type} post created successfully!")
            self.statusBar.showMessage("New post created.", 4000)
            self.load_posts_data() # Refresh the posts table
        except sqlite3.Error as e:
            logging.error(f"Database error creating post: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not create post: {e}")

    def edit_selected_post(self):
        """Opens the edit dialog for the single selected post."""
        selected_id = -1
        selected_post_type = ""
        found_count = 0

        # Iterate through all post tables to find the selected one
        for post_type, table in self.postsTables.items():
            ids = table.get_selected_post_ids()
            if ids:
                if found_count > 0 or len(ids) > 1: # Found selection in multiple tables or multiple rows
                    QMessageBox.warning(self, "Edit Post", "Please select only one post across all categories to edit.")
                    return
                selected_id = ids[0]
                selected_post_type = post_type
                found_count = 1

        if selected_id == -1:
            QMessageBox.information(self, "Edit Post", "No post selected for editing.")
            return

        # Find the PostData object
        post_to_edit = None
        if selected_post_type in self.all_posts_data:
            post_to_edit = next((p for p in self.all_posts_data[selected_post_type] if p.id == selected_id), None)

        if not post_to_edit:
            logging.error(f"Selected post ID {selected_id} not found in internal data for type {selected_post_type}.")
            QMessageBox.warning(self, "Edit Error", "Could not find the data for the selected post.")
            return

        # Ensure the correct tab is visible (optional, but good UX)
        if self.postsTabs.currentWidget() != self.postsTables[selected_post_type]:
             self.postsTabs.setCurrentWidget(self.postsTables[selected_post_type])

        # Open the dialog in edit mode
        dlg = PostDialogBase(post_type=selected_post_type, post_data=post_to_edit, parent=self)
        dlg.postSaved.connect(self.save_edited_post_data)
        dlg.exec_()

    def save_edited_post_data(self, post_data: PostData):
         """Receives data from Edit dialog and updates DB."""
         if post_data.id == -1:
              logging.error("Attempted to save edited post with invalid ID (-1).")
              return

         try:
            with QMutexLocker(db_mutex):
                 c.execute('''
                    UPDATE posts SET
                        content=?, recipe_text=?, website_link=?, image_path=?, post_type=?
                    WHERE id=?
                 ''', (
                    post_data.content,
                    post_data.recipe_text,
                    post_data.website_link,
                    post_data.image_path,
                    post_data.post_type, # Type generally shouldn't change, but include just in case
                    post_data.id
                 ))
                 conn.commit()
            logging.info(f"Post ID {post_data.id} updated successfully.")
            QMessageBox.information(self, "Success", "Post updated successfully!")
            self.statusBar.showMessage(f"Post {post_data.display_pid} updated.", 4000)
            self.load_posts_data() # Refresh tables
         except sqlite3.Error as e:
            logging.error(f"Database error updating post ID {post_data.id}: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not update post: {e}")


    def remove_selected_posts(self):
        """Removes selected posts from DB and UI."""
        ids_to_remove = []
        # Collect IDs from all tables
        for table in self.postsTables.values():
             ids_to_remove.extend(table.get_selected_post_ids())

        if not ids_to_remove:
            QMessageBox.information(self, "Remove Posts", "No posts selected to remove.")
            return

        # Remove duplicates if collected from multiple tables (shouldn't happen with single selection)
        ids_to_remove = list(set(ids_to_remove))

        reply = QMessageBox.question(
            self,
            "Confirm Removal",
            f"Are you sure you want to remove {len(ids_to_remove)} selected post(s)?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                with QMutexLocker(db_mutex):
                    c.executemany("DELETE FROM posts WHERE id=?", [(pid,) for pid in ids_to_remove])
                    conn.commit()
                logging.info(f"Removed {len(ids_to_remove)} posts: {ids_to_remove}")
                self.statusBar.showMessage(f"{len(ids_to_remove)} post(s) removed.", 4000)
                self.load_posts_data() # Refresh UI
            except sqlite3.Error as e:
                logging.error(f"Database error removing posts: {e}")
                QMessageBox.critical(self, "Database Error", f"Could not remove posts: {e}")

    def update_post_statuses_ui(self, updated_post_ids: list[int]):
        """Updates the status column for specific posts in the UI."""
        logging.debug(f"Updating UI for post IDs: {updated_post_ids}")
        if not hasattr(self, 'postsTables'): return

        try:
            with QMutexLocker(db_mutex):
                 # Fetch new statuses for the updated posts
                 placeholders = ','.join('?' * len(updated_post_ids))
                 rows = c.execute(f"SELECT id, status, post_type FROM posts WHERE id IN ({placeholders})", updated_post_ids).fetchall()

            status_map = {row['id']: (row['status'], row['post_type']) for row in rows}

            # Update relevant tables
            for post_type, table in self.postsTables.items():
                table.setUpdatesEnabled(False)
                status_col_idx = -1
                # Find status column index dynamically
                for i, col_info in enumerate(table.post_columns):
                     if col_info['key'] == 'status':
                          status_col_idx = i
                          break
                if status_col_idx == -1: continue # Skip if no status column

                # Iterate through visible rows and update if ID matches
                for row_idx in range(table.rowCount()):
                     item = table.item(row_idx, 0) # Assuming PID is column 0
                     if item:
                          post_id = item.data(Qt.UserRole) # Get DB ID stored in UserRole
                          if post_id in status_map:
                               new_status, ptype = status_map[post_id]
                               if ptype == table.post_type: # Ensure it's the correct table
                                    status_item = table.item(row_idx, status_col_idx)
                                    if status_item:
                                         status_item.setText(new_status)
                                         # Reapply color (or handle in populate_table's color logic)
                                         # ... (update background color based on new_status) ...
                                    logging.debug(f"Updated status for Post ID {post_id} to '{new_status}' in UI.")
                table.setUpdatesEnabled(True)

        except sqlite3.Error as e:
            logging.error(f"DB error fetching updated post statuses: {e}")
        except Exception as e:
             logging.error(f"Error updating post statuses UI: {e}", exc_info=True)


    # --- Schedule Management Actions ---

    def open_create_schedule_dialog(self):
        """Opens the dialog to define and generate a new schedule."""
        dlg = CreateScheduleDialog(self)
        dlg.scheduleCreated.connect(self.save_new_schedule_data)
        dlg.exec_()

    def save_new_schedule_data(self, schedule_list: list):
         """Saves the generated schedule list to the DB and adds a tab."""
         if not schedule_list:
              logging.warning("Attempted to save an empty schedule list.")
              return

         try:
              schedule_json = json.dumps(schedule_list) # Convert list to JSON string
              with QMutexLocker(db_mutex):
                   cursor = c.execute("INSERT INTO schedules (schedule_json) VALUES (?)", (schedule_json,))
                   conn.commit()
                   new_schedule_id = cursor.lastrowid
              logging.info(f"New schedule (ID: {new_schedule_id}) saved successfully.")
              self.statusBar.showMessage("New schedule created.", 4000)

              # Add the new schedule as a tab
              # If placeholder exists, remove it first
              if self.schedulesTabWidget.tabText(0) == "No Schedules":
                    self.schedulesTabWidget.removeTab(0)
                    self.schedulesTabWidget.setTabsClosable(True) # Enable closing again

              self._add_schedule_tab(schedule_list, new_schedule_id, f"Schedule {new_schedule_id}")
              self.schedulesTabWidget.setCurrentIndex(self.schedulesTabWidget.count() - 1) # Switch to new tab

         except json.JSONDecodeError as json_err:
              logging.error(f"Failed to serialize schedule data to JSON: {json_err}")
              QMessageBox.critical(self, "Save Error", f"Could not serialize schedule data: {json_err}")
         except sqlite3.Error as db_err:
              logging.error(f"Database error saving new schedule: {db_err}")
              QMessageBox.critical(self, "Database Error", f"Could not save new schedule: {db_err}")

    def _add_schedule_tab(self, schedule_data: list, schedule_id: int, tab_name: str):
        """Adds a new tab with a table for the given schedule data."""
        tab_widget = QWidget()
        tab_layout = QVBoxLayout(tab_widget)
        tab_layout.setContentsMargins(5, 5, 5, 5)
        tab_layout.setSpacing(8)

        # --- Filters ---
        filter_widget = QWidget()
        filter_layout = QHBoxLayout(filter_widget)
        filter_layout.setContentsMargins(0, 0, 0, 0)
        filter_layout.setSpacing(10)

        # Prepare filter options based on actual data
        all_times = sorted(list(set(entry.get("time", "N/A") for entry in schedule_data)), key=lambda x: datetime.datetime.strptime(x, "%I:%M %p").time() if x != "N/A" else datetime.time.max)
        all_dates = sorted(list(set(entry.get("date", "N/A") for entry in schedule_data)))
        all_categories = sorted(list(set(entry.get("category", "N/A") for entry in schedule_data)))
        all_targets = sorted(list(set(entry.get("item_name", "N/A") for entry in schedule_data)))

        # Time Filter
        filter_layout.addWidget(QLabel("Time:"))
        time_filter_combo = QComboBox()
        time_filter_combo.addItem("All Times")
        time_filter_combo.addItems(all_times)
        filter_layout.addWidget(time_filter_combo)

        # Date Filter (Simple date selection for now)
        filter_layout.addWidget(QLabel("Date:"))
        date_filter_combo = QComboBox()
        date_filter_combo.addItem("All Dates")
        date_filter_combo.addItems(all_dates)
        filter_layout.addWidget(date_filter_combo)

        # Category Filter
        filter_layout.addWidget(QLabel("Category:"))
        category_filter_combo = QComboBox()
        category_filter_combo.addItem("All Categories")
        category_filter_combo.addItems(all_categories)
        filter_layout.addWidget(category_filter_combo)

        # Target Filter
        filter_layout.addWidget(QLabel("Target:"))
        target_filter_combo = QComboBox()
        target_filter_combo.addItem("All Targets")
        target_filter_combo.addItems(all_targets)
        filter_layout.addWidget(target_filter_combo)

        filter_layout.addStretch()
        tab_layout.addWidget(filter_widget)

        # --- Table ---
        schedule_table = QTableWidget()
        schedule_table.setColumnCount(8)
        schedule_table.setHorizontalHeaderLabels(["Post ID", "Date", "Time", "Target", "Content", "Images", "Category", ""]) # Last is checkbox
        # Configure header resizing
        sched_header = schedule_table.horizontalHeader()
        sched_header.setSectionResizeMode(4, QHeaderView.Stretch) # Content
        sched_header.setSectionResizeMode(7, QHeaderView.Fixed) # Checkbox
        sched_header.resizeSection(7, 30)
        sched_header.setSectionResizeMode(0, QHeaderView.Interactive) # PID
        sched_header.resizeSection(0, 100)
        sched_header.setSectionResizeMode(1, QHeaderView.Interactive) # Date
        sched_header.resizeSection(1, 100)
        sched_header.setSectionResizeMode(2, QHeaderView.Interactive) # Time
        sched_header.resizeSection(2, 80)
        sched_header.setSectionResizeMode(3, QHeaderView.Interactive) # Target
        sched_header.resizeSection(3, 150)
        sched_header.setSectionResizeMode(5, QHeaderView.Interactive) # Images
        sched_header.resizeSection(5, 120)
        sched_header.setSectionResizeMode(6, QHeaderView.Interactive) # Category
        sched_header.resizeSection(6, 100)

        schedule_table.setSelectionMode(QAbstractItemView.NoSelection)
        schedule_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        schedule_table.setAlternatingRowColors(True)
        schedule_table.verticalHeader().setVisible(False)
        tab_layout.addWidget(schedule_table)

        # Add tab to the main widget
        index = self.schedulesTabWidget.addTab(tab_widget, QIcon.fromTheme("view-calendar-month"), tab_name)

        # Store data and widgets associated with this tab index
        tab_info = {
            "id": schedule_id,
            "data": schedule_data, # The original full data list
            "table": schedule_table,
            "filters": {
                "time": time_filter_combo,
                "date": date_filter_combo,
                "category": category_filter_combo,
                "target": target_filter_combo,
            },
            "filtered_indices": list(range(len(schedule_data))) # Indices currently shown in table
        }
        self.all_schedules_data[index] = tab_info

        # Connect filter signals
        time_filter_combo.currentIndexChanged.connect(lambda: self.apply_schedule_filters(index))
        date_filter_combo.currentIndexChanged.connect(lambda: self.apply_schedule_filters(index))
        category_filter_combo.currentIndexChanged.connect(lambda: self.apply_schedule_filters(index))
        target_filter_combo.currentIndexChanged.connect(lambda: self.apply_schedule_filters(index))

        # Initial population
        self.apply_schedule_filters(index)


    def apply_schedule_filters(self, tab_index):
        """Filters data based on combo boxes and updates the table."""
        if tab_index not in self.all_schedules_data:
             logging.warning(f"Apply filters called for invalid tab index: {tab_index}")
             return

        tab_info = self.all_schedules_data[tab_index]
        full_data = tab_info["data"]
        table = tab_info["table"]
        filters = tab_info["filters"]

        # Get filter values
        time_filter = filters["time"].currentText()
        date_filter = filters["date"].currentText()
        category_filter = filters["category"].currentText()
        target_filter = filters["target"].currentText()

        filtered_indices = []
        for idx, entry in enumerate(full_data):
            # Apply filters - skip entry if it doesn't match
            if time_filter != "All Times" and entry.get("time") != time_filter:
                continue
            if date_filter != "All Dates" and entry.get("date") != date_filter:
                continue
            if category_filter != "All Categories" and entry.get("category") != category_filter:
                continue
            if target_filter != "All Targets" and entry.get("item_name") != target_filter:
                continue

            # If all filters pass, add the original index
            filtered_indices.append(idx)

        # Store the filtered indices
        tab_info["filtered_indices"] = filtered_indices

        # Update the table UI
        table.setUpdatesEnabled(False)
        table.setSortingEnabled(False)
        table.clearContents()
        table.setRowCount(len(filtered_indices))

        category_colors = { # Use consistent colors
            "Recipes": QColor("#FFE0E6"), # Light pink
            "Engage": QColor("#E0FFE0"), # Light green
            "Parole": QColor("#E0F2FF"), # Light blue
        }
        default_color = QColor(Qt.white)

        for row_idx, original_index in enumerate(filtered_indices):
             entry = full_data[original_index]

             # Populate table cells (similar to Posts table, adapt as needed)
             pid_item = QTableWidgetItem(entry.get("post_id", "N/A"))
             pid_item.setTextAlignment(Qt.AlignCenter)
             table.setItem(row_idx, 0, pid_item)

             table.setItem(row_idx, 1, QTableWidgetItem(entry.get("date", "N/A")))
             table.setItem(row_idx, 2, QTableWidgetItem(entry.get("time", "N/A")))
             table.setItem(row_idx, 3, QTableWidgetItem(entry.get("item_name", "N/A")))

             content = entry.get("post_content", "")
             content_display = content[:80] + "..." if len(content) > 80 else content
             content_item = QTableWidgetItem(content_display)
             content_item.setToolTip(content)
             table.setItem(row_idx, 4, content_item)

             # Images (similar to Posts table)
             images_str = entry.get("post_images", "")
             image_paths = [p.strip() for p in images_str.split(',') if p.strip()] if images_str else []
             # (Code for thumbnail widget creation - reuse from Posts table logic)
             # ... create thumb_widget ...
             # table.setCellWidget(row_idx, 5, thumb_widget)
             # For now, just put text:
             img_item = QTableWidgetItem(f"{len(image_paths)} image(s)" if image_paths else "N/A")
             img_item.setTextAlignment(Qt.AlignCenter)
             table.setItem(row_idx, 5, img_item)


             category = entry.get("category", "N/A")
             cat_item = QTableWidgetItem(category)
             cat_item.setTextAlignment(Qt.AlignCenter)
             table.setItem(row_idx, 6, cat_item)

             # Checkbox (link state to the original data entry)
             chk = QCheckBox()
             chk.setChecked(entry.get("checked", False))
             # Store original index to modify the correct data entry
             chk.setProperty("original_index", original_index)
             chk.stateChanged.connect(lambda state, idx=original_index: self._schedule_entry_state_changed(tab_index, idx, state))

             chk_container = QWidget()
             chk_layout = QHBoxLayout(chk_container)
             chk_layout.addWidget(chk, alignment=Qt.AlignCenter)
             chk_layout.setContentsMargins(0, 0, 0, 0)
             table.setCellWidget(row_idx, 7, chk_container)

             # Apply row color
             row_color = category_colors.get(category, default_color)
             for col in range(table.columnCount()):
                 if table.item(row_idx, col):
                     table.item(row_idx, col).setBackground(row_color)
                 if table.cellWidget(row_idx, col):
                     table.cellWidget(row_idx, col).setStyleSheet(f"background-color: {row_color.name()};")


        table.setSortingEnabled(True)
        table.setUpdatesEnabled(True)
        logging.debug(f"Schedule tab {tab_index} updated with {len(filtered_indices)} rows.")

    def _schedule_entry_state_changed(self, tab_index, original_index, state):
         """Updates the 'checked' state in the underlying data list."""
         if tab_index in self.all_schedules_data:
              if 0 <= original_index < len(self.all_schedules_data[tab_index]["data"]):
                   self.all_schedules_data[tab_index]["data"][original_index]["checked"] = (state == Qt.Checked)
              else:
                   logging.warning(f"Invalid original_index {original_index} for schedule state change.")
         else:
              logging.warning(f"Invalid tab_index {tab_index} for schedule state change.")


    def remove_schedule_tab(self, index):
        """Handles the request to close a schedule tab."""
        if index not in self.all_schedules_data:
            # Might be the placeholder tab if implementation allows closing it
            if self.schedulesTabWidget.tabText(index) == "No Schedules":
                 # Don't remove placeholder this way
                 return
            logging.warning(f"Attempted to remove non-existent schedule tab index: {index}")
            return

        tab_info = self.all_schedules_data[index]
        schedule_id = tab_info["id"]
        tab_name = self.schedulesTabWidget.tabText(index)

        reply = QMessageBox.question(
            self, "Remove Schedule",
            f"Are you sure you want to remove '{tab_name}' (ID: {schedule_id})?\n"
            "This will delete the schedule permanently from the database.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                with QMutexLocker(db_mutex):
                    c.execute("DELETE FROM schedules WHERE id=?", (schedule_id,))
                    conn.commit()
                logging.info(f"Removed schedule ID {schedule_id} from database.")
                self.statusBar.showMessage(f"Schedule '{tab_name}' removed.", 4000)

                # Remove from internal data and UI
                del self.all_schedules_data[index]
                self.schedulesTabWidget.removeTab(index)

                 # After removing a tab, indices of subsequent tabs change.
                 # Need to rebuild self.all_schedules_data with correct indices.
                new_schedule_data = {}
                for i in range(self.schedulesTabWidget.count()):
                     # Find the original data based on schedule_id stored somewhere reliable,
                     # or update the keys of all_schedules_data. Simpler to reload for now.
                     pass # For now, rely on full reload or careful index management
                # Simplest fix: Reload all schedules after removal
                self.load_schedules_data()


                # Add placeholder if last tab was removed
                if self.schedulesTabWidget.count() == 0:
                     self._add_placeholder_schedule_tab()

                self._update_schedule_button_states() # Update button enable state

            except sqlite3.Error as e:
                logging.error(f"Database error removing schedule ID {schedule_id}: {e}")
                QMessageBox.critical(self, "Database Error", f"Could not remove schedule: {e}")


    def _get_current_schedule_tab_info(self) -> tuple[int, dict | None]:
         """Gets the index and data dict for the currently selected schedule tab."""
         current_index = self.schedulesTabWidget.currentIndex()
         if current_index == -1 or current_index not in self.all_schedules_data:
              return -1, None
         return current_index, self.all_schedules_data[current_index]


    def _update_schedule_button_states(self):
         """Enables/disables schedule action buttons based on current tab."""
         index, tab_info = self._get_current_schedule_tab_info()
         has_valid_tab = (tab_info is not None)

         self.selectAllScheduleBtn.setEnabled(has_valid_tab)
         self.deselectAllScheduleBtn.setEnabled(has_valid_tab)
         self.removeSelectedScheduleRowsBtn.setEnabled(has_valid_tab)


    def select_all_schedule_rows(self):
         """Selects all checkboxes in the current schedule table."""
         index, tab_info = self._get_current_schedule_tab_info()
         if not tab_info: return
         self._set_all_schedule_rows_checkstate(tab_info["table"], tab_info["filtered_indices"], True)

    def deselect_all_schedule_rows(self):
         """Deselects all checkboxes in the current schedule table."""
         index, tab_info = self._get_current_schedule_tab_info()
         if not tab_info: return
         self._set_all_schedule_rows_checkstate(tab_info["table"], tab_info["filtered_indices"], False)

    def _set_all_schedule_rows_checkstate(self, table, filtered_indices, checked):
         """Helper to set check state for all visible rows."""
         table.setUpdatesEnabled(False)
         for row_idx in range(table.rowCount()):
              widget = table.cellWidget(row_idx, 7) # Checkbox column
              if widget:
                   cb = widget.findChild(QCheckBox)
                   if cb:
                        # Update data model directly to avoid triggering signal loop
                        original_index = cb.property("original_index")
                        tab_index = self.schedulesTabWidget.currentIndex()
                        if tab_index in self.all_schedules_data and original_index is not None:
                             self.all_schedules_data[tab_index]["data"][original_index]["checked"] = checked

                        # Update UI checkbox (block signals if necessary)
                        # cb.blockSignals(True)
                        cb.setChecked(checked)
                        # cb.blockSignals(False)
         table.setUpdatesEnabled(True)

    def remove_selected_schedule_rows(self):
        """Removes rows checked in the current schedule table from the data and DB."""
        tab_index, tab_info = self._get_current_schedule_tab_info()
        if not tab_info:
            QMessageBox.warning(self, "Action Failed", "No schedule tab selected.")
            return

        schedule_id = tab_info["id"]
        full_data = tab_info["data"]
        indices_to_remove = [] # Store original indices from full_data

        # Find checked rows based on the data model (not just UI table)
        for i, entry in enumerate(full_data):
            if entry.get("checked", False):
                indices_to_remove.append(i)

        if not indices_to_remove:
            QMessageBox.information(self, "Remove Rows", "No rows selected to remove.")
            return

        reply = QMessageBox.question(
            self, "Confirm Row Removal",
            f"Are you sure you want to remove {len(indices_to_remove)} selected row(s) from this schedule?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Create the new data list excluding the removed items
            new_schedule_data = [entry for i, entry in enumerate(full_data) if i not in indices_to_remove]

            # Update the database
            try:
                new_schedule_json = json.dumps(new_schedule_data)
                with QMutexLocker(db_mutex):
                    c.execute("UPDATE schedules SET schedule_json=? WHERE id=?", (new_schedule_json, schedule_id))
                    conn.commit()
                logging.info(f"Removed {len(indices_to_remove)} rows from schedule ID {schedule_id}.")
                self.statusBar.showMessage(f"{len(indices_to_remove)} rows removed from schedule.", 4000)

                # Update the internal data store for the tab
                tab_info["data"] = new_schedule_data
                # Re-apply filters which will repopulate the table
                self.apply_schedule_filters(tab_index)

            except json.JSONDecodeError as json_err:
                 logging.error(f"Failed to serialize updated schedule data: {json_err}")
                 QMessageBox.critical(self, "Save Error", f"Could not serialize updated schedule data: {json_err}")
            except sqlite3.Error as db_err:
                 logging.error(f"Database error updating schedule ID {schedule_id} after row removal: {db_err}")
                 QMessageBox.critical(self, "Database Error", f"Could not update schedule: {db_err}")
                 # Consider reloading data from DB to revert UI state on error
                 self.load_schedules_data()


    def refresh_all_schedules(self):
         """Refreshes post content/images in all loaded schedules from the DB."""
         logging.info("Refreshing content for all schedules...")
         progress = QProgressDialog("Refreshing schedule contents...", "Cancel", 0, len(self.all_schedules_data), self)
         progress.setWindowModality(Qt.WindowModal)
         progress.setValue(0)

         updated_count = 0
         db_error = False
         post_cache = {} # Cache post details to reduce DB queries

         for idx, tab_info in self.all_schedules_data.items():
             if progress.wasCanceled(): break
             progress.setLabelText(f"Refreshing {self.schedulesTabWidget.tabText(idx)}...")

             schedule_id = tab_info["id"]
             schedule_data = tab_info["data"]
             needs_db_update = False

             for entry in schedule_data:
                 db_post_id = entry.get("db_post_id")
                 if db_post_id is None: continue # Skip entries without linked post ID

                 # Check cache first
                 cached_post = post_cache.get(db_post_id)

                 if cached_post is None:
                     # Not in cache, fetch from DB
                     try:
                         with QMutexLocker(db_mutex):
                             row = c.execute("SELECT content, image_path, status FROM posts WHERE id=?", (db_post_id,)).fetchone()
                         if row:
                              cached_post = {"content": row["content"], "image_path": row["image_path"], "status": row["status"]}
                              post_cache[db_post_id] = cached_post
                         else:
                              # Post deleted from DB? Mark as invalid in schedule?
                              cached_post = {"content": "[Post Deleted]", "image_path": "", "status": "deleted"}
                              post_cache[db_post_id] = cached_post # Cache the deleted state
                     except sqlite3.Error as e:
                          logging.error(f"DB error fetching post {db_post_id} during schedule refresh: {e}")
                          db_error = True
                          break # Stop refreshing this schedule on DB error

                 # Compare and update entry if changed
                 if entry.get("post_content") != cached_post["content"] or entry.get("post_images") != cached_post["image_path"]:
                     entry["post_content"] = cached_post["content"]
                     entry["post_images"] = cached_post["image_path"]
                     entry["post_status"] = cached_post["status"] # Optionally track post status
                     needs_db_update = True
                     updated_count += 1

             # Update schedule JSON in DB if changes were made
             if needs_db_update and not db_error:
                  try:
                      new_schedule_json = json.dumps(schedule_data)
                      with QMutexLocker(db_mutex):
                           c.execute("UPDATE schedules SET schedule_json=? WHERE id=?", (new_schedule_json, schedule_id))
                           conn.commit()
                      logging.debug(f"Updated schedule {schedule_id} in DB after refresh.")
                  except Exception as e:
                       logging.error(f"Error saving refreshed schedule {schedule_id} to DB: {e}")
                       QMessageBox.warning(self, "Save Error", f"Could not save updates for schedule {schedule_id} after refresh.")
                       # Continue refreshing other schedules

             # Update UI for the current tab if it's visible
             if self.schedulesTabWidget.currentIndex() == idx:
                   self.apply_schedule_filters(idx) # Re-apply filters to show refreshed data

             if db_error: break # Stop outer loop if DB error occurred
             progress.setValue(progress.value() + 1)

         progress.close()

         if db_error:
              QMessageBox.critical(self, "Database Error", "A database error occurred while refreshing schedules. Check logs.")
         elif updated_count > 0:
              QMessageBox.information(self, "Refresh Complete", f"Refreshed content for {updated_count} schedule entries across all loaded schedules.")
              # Ensure the currently visible tab is refreshed if it wasn't updated mid-loop
              current_idx = self.schedulesTabWidget.currentIndex()
              if current_idx in self.all_schedules_data:
                  self.apply_schedule_filters(current_idx)
         elif not progress.wasCanceled():
              QMessageBox.information(self, "Refresh Complete", "No schedule content needed updating.")

         self.statusBar.showMessage("Schedule refresh finished.", 4000)


    # --- Pages/Groups Manager Actions ---

    def fetch_pages_or_groups_for_manager(self, fetch_pages: bool):
        """Fetches Pages or Groups using Selenium worker."""
        action = "Pages" if fetch_pages else "Groups"
        target_table = self.pagesTable if fetch_pages else self.groupsTable
        target_func = _fetch_pages_selenium if fetch_pages else _fetch_groups_selenium
        fetch_button = self.pagesFetchBtn if fetch_pages else self.groupsFetchBtn

        # 1. Select Account
        if not self.all_accounts_data:
            QMessageBox.warning(self, f"Fetch {action}", "No accounts available. Please add an account first.")
            return

        # Use name + UID for clarity in selection
        account_items = [f"{acc.full_name} ({acc.fb_id})" for acc in self.all_accounts_data]
        # Ensure default selection is valid
        current_selection = 0 if account_items else -1

        item, ok = QInputDialog.getItem(self, f"Select Account",
                                        f"Fetch {action} for which account?",
                                        account_items, current_selection, False)
        if not ok or not item:
            return # User cancelled

        # Find selected AccountData object
        selected_account_index = account_items.index(item)
        selected_account = self.all_accounts_data[selected_account_index]

        if not selected_account.cookie:
             QMessageBox.warning(self, "Missing Cookie", f"Account '{selected_account.full_name}' does not have cookie data. Cannot fetch {action}.")
             return

        # 2. Run Fetch in Worker Thread
        fetch_button.setEnabled(False)
        fetch_button.setText(f"Fetching {action}...")
        self.statusBar.showMessage(f"Fetching {action} for {selected_account.full_name}...")

        self.fetch_worker = BaseWorker(target_func, selected_account.cookie)
        # Connect finished signal to a handler specific to pages/groups
        handler = self.on_fetch_pages_finished if fetch_pages else self.on_fetch_groups_finished
        self.fetch_worker.finished.connect(handler)
        self.fetch_worker.progress.connect(self.statusBar.showMessage)
        self.fetch_worker.start()


    def on_fetch_pages_finished(self, result, error):
        """Handles result of fetching pages."""
        self.pagesFetchBtn.setEnabled(True)
        self.pagesFetchBtn.setText("Fetch Pages from Account")
        if error:
             QMessageBox.critical(self, "Fetch Pages Failed", f"Could not fetch pages.\nError: {error}\nCheck logs for details.")
             self.statusBar.showMessage("Fetching pages failed.", 5000)
        elif result is not None:
             self._update_page_group_table(self.pagesTable, result)
             QMessageBox.information(self, "Fetch Complete", f"Fetched {len(result)} pages.")
             self.statusBar.showMessage(f"Fetched {len(result)} pages.", 5000)
        else:
             QMessageBox.warning(self, "Fetch Pages", "Fetching process finished with no data or unexpected result.")
             self.statusBar.showMessage("Fetching pages finished with no data.", 5000)


    def on_fetch_groups_finished(self, result, error):
        """Handles result of fetching groups."""
        self.groupsFetchBtn.setEnabled(True)
        self.groupsFetchBtn.setText("Fetch Groups from Account")
        if error:
            QMessageBox.critical(self, "Fetch Groups Failed", f"Could not fetch groups.\nError: {error}\nCheck logs for details.")
            self.statusBar.showMessage("Fetching groups failed.", 5000)
        elif result is not None:
            self._update_page_group_table(self.groupsTable, result)
            QMessageBox.information(self, "Fetch Complete", f"Fetched {len(result)} groups.")
            self.statusBar.showMessage(f"Fetched {len(result)} groups.", 5000)
        else:
            QMessageBox.warning(self, "Fetch Groups", "Fetching process finished with no data or unexpected result.")
            self.statusBar.showMessage("Fetching groups finished with no data.", 5000)


    def _update_page_group_table(self, table: QTableWidget, data_list: list[dict]):
         """Populates the Pages or Groups table."""
         table.setUpdatesEnabled(False)
         table.setSortingEnabled(False)
         table.clearContents()
         table.setRowCount(len(data_list))

         for row_idx, item_data in enumerate(data_list):
             name = item_data.get("name", "N/A")
             item_id = item_data.get("id", "N/A")
             link = item_data.get("link", "")

             # Checkbox
             cb = QCheckBox()
             cb_container = QWidget()
             cb_layout = QHBoxLayout(cb_container)
             cb_layout.addWidget(cb, alignment=Qt.AlignCenter)
             cb_layout.setContentsMargins(0, 0, 0, 0)
             table.setCellWidget(row_idx, 0, cb_container)

             # Name
             name_item = QTableWidgetItem(name)
             name_item.setToolTip(name)
             table.setItem(row_idx, 1, name_item)

             # ID
             id_item = QTableWidgetItem(item_id)
             table.setItem(row_idx, 2, id_item)

             # Link
             link_item = QTableWidgetItem(link)
             link_item.setToolTip("Double-click to open link")
             table.setItem(row_idx, 3, link_item)
             if not link: # Disable link if empty
                  link_item.setFlags(link_item.flags() & ~Qt.ItemIsEnabled)

         table.setSortingEnabled(True)
         table.setUpdatesEnabled(True)

    def _select_all_table_rows(self, table: QTableWidget, select: bool):
         """Generic helper to select/deselect all checkboxes in column 0 of a table."""
         for row in range(table.rowCount()):
              widget = table.cellWidget(row, 0)
              if widget:
                   cb = widget.findChild(QCheckBox)
                   if cb:
                        cb.setChecked(select)

    def open_page_or_group_link(self, item: QTableWidgetItem):
        """Opens the link in column 3 when a cell is double-clicked."""
        if item.column() == 3: # Link column
            link_url = item.text()
            if link_url and link_url.startswith("http"):
                try:
                    QDesktopServices.openUrl(QUrl(link_url))
                except Exception as e:
                    logging.error(f"Failed to open URL {link_url}: {e}")
                    QMessageBox.warning(self, "Open Link Failed", f"Could not open the link:\n{link_url}\n\nError: {e}")
            elif link_url:
                 QMessageBox.warning(self, "Invalid Link", f"The link is not valid:\n{link_url}")


    # --- Background Post Checking ---

    def run_post_check_worker(self):
        """Starts the CheckPostsWorker if not already running."""
        if self.post_check_worker_instance and self.post_check_worker_instance.isRunning():
            logging.debug("CheckPostsWorker is already running.")
            return

        logging.debug("Starting CheckPostsWorker...")
        self.post_check_worker_instance = CheckPostsWorker()
        self.post_check_worker_instance.postsStatusChanged.connect(self.update_post_statuses_ui)
        self.post_check_worker_instance.errorOccurred.connect(self._handle_worker_error)
        # Clean up thread object when finished
        self.post_check_worker_instance.finished.connect(self.post_check_worker_instance.deleteLater)
        self.post_check_worker_instance.start()

    def _handle_worker_error(self, error_message):
        """Displays errors reported by worker threads."""
        logging.error(f"Background worker error: {error_message}")
        self.statusBar.showMessage(f"Worker Error: {error_message}", 6000)
        # Potentially show a persistent warning or disable related features

    # --- Reset Functionality ---

    def resetSoftware(self):
        """Resets the application data after confirmation."""
        text, ok = QInputDialog.getText(self, "Confirm Reset",
                                        "This action is irreversible and will delete the database, logs, and account files.\n\n"
                                        "Type 'RESET NOW' (case-sensitive) to confirm:",
                                        QLineEdit.Normal, "")
        if ok and text == "RESET NOW":
            reply = QMessageBox.warning(
                self,
                "Final Confirmation",
                "Are you absolutely sure you want to reset all software data?",
                QMessageBox.Yes | QMessageBox.Cancel,
                QMessageBox.Cancel
            )
            if reply == QMessageBox.Yes:
                logging.warning("Initiating software reset...")
                self.statusBar.showMessage("Resetting software... Application will close.")
                QApplication.processEvents() # Show message

                # 1. Stop Timers and Workers Safely
                self.post_check_timer.stop()
                if self.post_check_worker_instance:
                    self.post_check_worker_instance.stop()
                    # Wait briefly for the worker to finish cleanly if possible
                    self.post_check_worker_instance.wait(2000) # Wait up to 2 seconds

                # 2. Close Database Connection
                # Use a lock to ensure no other thread is using it right before close
                logging.debug("Closing main database connection...")
                locked = db_mutex.tryLock(1000) # Try locking for 1 sec
                try:
                    if locked:
                        if conn:
                            conn.close()
                            logging.info("Main database connection closed.")
                    else:
                        logging.warning("Could not acquire DB mutex to close connection cleanly during reset.")
                finally:
                    if locked: db_mutex.unlock()

                # 3. Shutdown Logging (to release log file handle)
                logging.info("Shutting down logging system...")
                logging.shutdown()

                # 4. Delete Files (Database, Logs, Account JSONs)
                files_to_delete = [DB_FILE, LOG_FILE]
                # Find account files (e.g., account_*.json)
                account_files = glob.glob("account_*.json")
                files_to_delete.extend(account_files)

                deleted_files = []
                failed_files = []
                for f in files_to_delete:
                    try:
                        if os.path.exists(f):
                            os.remove(f)
                            deleted_files.append(os.path.basename(f))
                    except OSError as e:
                        # Log to stderr as logging is shut down
                        print(f"RESET ERROR: Failed to delete file {f}: {e}", file=sys.stderr)
                        failed_files.append(os.path.basename(f))

                # 5. Show Final Message and Quit
                final_message = "Software reset complete.\n"
                if deleted_files:
                    final_message += f"Deleted: {', '.join(deleted_files)}\n"
                if failed_files:
                    final_message += f"Failed to delete: {', '.join(failed_files)}\nCheck permissions.\n"
                final_message += "\nPlease restart the application."

                # Use a simple message box as the main loop might be unstable
                msgBox = QMessageBox(QMessageBox.Information, "Reset Complete", final_message)
                msgBox.exec_()

                # Force exit after reset (safer than relying on normal close)
                os._exit(0) # Use os._exit for immediate termination

        elif ok: # User entered text but it wasn't correct
            QMessageBox.warning(self, "Reset Cancelled", "Incorrect confirmation text. Software reset has been cancelled.")


    # --- Cleanup ---
    def closeEvent(self, event):
        """Handles application closing."""
        logging.info("Close event triggered. Shutting down...")
        self.statusBar.showMessage("Closing application...")

        # Stop timers
        self.post_check_timer.stop()

        # Stop worker threads
        if self.post_check_worker_instance:
            self.post_check_worker_instance.stop()
            # Wait a short time for worker to finish file/db operations
            if not self.post_check_worker_instance.wait(2000): # Wait max 2 secs
                 logging.warning("CheckPostsWorker did not finish cleanly.")

        # Close DB connection (ensure safety with mutex)
        logging.debug("Closing main database connection on exit...")
        locked = db_mutex.tryLock(1000)
        try:
             if locked:
                  if conn:
                       conn.close()
                       logging.info("Main database connection closed.")
             else:
                  logging.warning("Could not acquire DB mutex to close connection on exit.")
        finally:
             if locked: db_mutex.unlock()

        logging.info("Application shutdown complete.")
        event.accept()


# --- Main Execution ---
if __name__ == "__main__":
    # Enable High DPI scaling for better visuals on modern displays
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # Set app details (optional, for window managers)
    # Use QApplication since it inherits from QCoreApplication
    QApplication.setApplicationName("FacebookScheduler")
    QApplication.setOrganizationName("YourOrg") # Replace if applicable

    app = QApplication(sys.argv)

    # Check if database connection was successful during init
    if not conn:
         # Error already logged/printed in initialize_database
         # Optionally show a simple GUI message box here too if needed,
         # but exiting might be sufficient as console shows the critical error.
         sys.exit(1)

    # --- Load FontAwesome or other Icons (Optional) ---
    # ... (icon loading code remains the same)

    window = FacebookSchedulerMain()
    window.show()
    sys.exit(app.exec_())